"""
Tests for Phase 3 Contact Management functionality.

This module tests the enhanced contact management features including
VCF generation, device sync, social media integration, and analytics.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import Mock, patch
import json

from app.main import app
from app.db.database import get_db
from app.api.auth import get_current_user
from app.db.models import Contact, SocialPlatform
from app.utils.vcf_generator import generate_vcf_content, generate_vcf_filename


# Test fixtures
@pytest.fixture
def mock_db():
    """Mock database session"""
    return Mock(spec=Session)

@pytest.fixture
def mock_user():
    """Mock authenticated user"""
    return {"uid": "test_user_123", "email": "<EMAIL>"}

@pytest.fixture
def test_contact():
    """Test contact object"""
    contact = Mock(spec=Contact)
    contact.id = "contact_123"
    contact.name = "John Doe"
    contact.phone = "+1234567890"
    contact.email = "<EMAIL>"
    contact.location = "Coffee Shop"
    contact.met_at = "Networking event"
    contact.social_media = {"twitter": "johndoe", "linkedin": "john-doe"}
    contact.memory_prompt = "Software engineer interested in AI"
    contact.image_path = "https://example.com/image.jpg"
    contact.device_sync_status = "synced"
    contact.owner_id = "test_user_123"
    contact.created_at = "2024-01-15T10:30:00Z"
    contact.updated_at = "2024-01-15T10:30:00Z"
    contact.synced_at = "2024-01-15T10:30:00Z"
    return contact

@pytest.fixture
def client():
    """Test client with mocked dependencies"""
    def mock_get_db():
        return Mock(spec=Session)
    
    def mock_get_current_user():
        return {"uid": "test_user_123", "email": "<EMAIL>"}
    
    app.dependency_overrides[get_db] = mock_get_db
    app.dependency_overrides[get_current_user] = mock_get_current_user
    
    with TestClient(app) as client:
        yield client
    
    # Clean up
    app.dependency_overrides.clear()


class TestContactCRUD:
    """Test enhanced contact CRUD operations"""
    
    def test_create_contact_with_new_fields(self, client):
        """Test creating a contact with all new Phase 3 fields"""
        contact_data = {
            "name": "Jane Smith",
            "phone": "+1987654321",
            "email": "<EMAIL>",
            "location": "Tech Conference",
            "met_at": "AI Summit 2024",
            "social_media": {
                "twitter": "janesmith",
                "instagram": "jane.smith"
            },
            "memory_prompt": "AI researcher working on NLP",
            "device_sync_status": "disabled"
        }
        
        with patch('app.db.crud.create_contact') as mock_create:
            mock_contact = Mock()
            mock_contact.id = "new_contact_123"
            mock_contact.name = contact_data["name"]
            mock_create.return_value = mock_contact
            
            response = client.post("/api/v1/contacts/", json=contact_data)
            
            assert response.status_code == 201
            mock_create.assert_called_once()
    
    def test_delete_contact(self, client):
        """Test soft delete functionality"""
        contact_id = "contact_123"
        
        with patch('app.db.crud.delete_contact') as mock_delete:
            mock_delete.return_value = True
            
            response = client.delete(f"/api/v1/contacts/{contact_id}")
            
            assert response.status_code == 204
            mock_delete.assert_called_once_with(
                db=Mock(),
                contact_id=contact_id,
                user_id="test_user_123",
                soft_delete=True
            )


class TestVCFGeneration:
    """Test VCF file generation functionality"""
    
    def test_generate_vcf_content(self, test_contact):
        """Test VCF content generation"""
        vcf_content = generate_vcf_content(test_contact)
        
        assert "BEGIN:VCARD" in vcf_content
        assert "VERSION:3.0" in vcf_content
        assert "FN:John Doe" in vcf_content
        assert "TEL;TYPE=CELL:+1234567890" in vcf_content
        assert "EMAIL;TYPE=HOME:<EMAIL>" in vcf_content
        assert "ORG:Met at: Coffee Shop" in vcf_content
        assert "URL:https://twitter.com/johndoe" in vcf_content
        assert "URL:https://linkedin.com/in/john-doe" in vcf_content
        assert "NOTE:Software engineer interested in AI" in vcf_content
        assert "END:VCARD" in vcf_content
    
    def test_generate_vcf_filename(self, test_contact):
        """Test VCF filename generation"""
        filename = generate_vcf_filename(test_contact)
        assert filename == "John_Doe.vcf"
    
    def test_vcf_endpoint(self, client):
        """Test VCF download endpoint"""
        contact_id = "contact_123"
        
        with patch('app.db.crud.get_contact_by_id') as mock_get, \
             patch('app.utils.vcf_generator.generate_vcf_content') as mock_vcf:
            
            mock_contact = Mock()
            mock_contact.name = "John Doe"
            mock_get.return_value = mock_contact
            mock_vcf.return_value = "BEGIN:VCARD\nVERSION:3.0\nFN:John Doe\nEND:VCARD"
            
            response = client.get(f"/api/v1/contacts/{contact_id}/vcf")
            
            assert response.status_code == 200
            assert response.headers["content-type"] == "text/vcard; charset=utf-8"
            assert "attachment" in response.headers["content-disposition"]


class TestDeviceSync:
    """Test device synchronization functionality"""
    
    def test_update_device_sync_status(self, client):
        """Test updating device sync status"""
        contact_id = "contact_123"
        sync_data = {
            "device_contact_id": "device_456",
            "sync_status": "synced",
            "device_info": {"platform": "android", "version": "14"}
        }
        
        with patch('app.db.crud.update_contact_sync_status') as mock_update:
            mock_contact = Mock()
            mock_contact.device_sync_status = "synced"
            mock_contact.synced_at = "2024-01-15T10:30:00Z"
            mock_update.return_value = mock_contact
            
            response = client.post(
                f"/api/v1/contacts/{contact_id}/device-sync",
                json=sync_data
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["contact_id"] == contact_id
            assert data["sync_status"] == "synced"
    
    def test_invalid_sync_status(self, client):
        """Test validation of sync status"""
        contact_id = "contact_123"
        sync_data = {
            "sync_status": "invalid_status"
        }
        
        response = client.post(
            f"/api/v1/contacts/{contact_id}/device-sync",
            json=sync_data
        )
        
        assert response.status_code == 400
        assert "Invalid sync status" in response.json()["detail"]


class TestContactSearch:
    """Test advanced contact search functionality"""
    
    def test_search_contacts(self, client):
        """Test contact search with filters"""
        search_params = {
            "q": "john",
            "platform": "twitter",
            "limit": 10,
            "offset": 0
        }
        
        with patch('app.db.crud.search_contacts') as mock_search:
            mock_contacts = [Mock()]
            mock_search.return_value = mock_contacts
            
            response = client.get("/api/v1/contacts/search", params=search_params)
            
            assert response.status_code == 200
            data = response.json()
            assert "results" in data
            assert "total" in data
            assert data["query"] == "john"
    
    def test_search_with_date_filters(self, client):
        """Test search with date range filters"""
        search_params = {
            "q": "test",
            "date_from": "2024-01-01T00:00:00Z",
            "date_to": "2024-12-31T23:59:59Z"
        }
        
        with patch('app.db.crud.search_contacts') as mock_search:
            mock_search.return_value = []
            
            response = client.get("/api/v1/contacts/search", params=search_params)
            
            assert response.status_code == 200


class TestContactAnalytics:
    """Test contact analytics functionality"""
    
    def test_get_contact_analytics(self, client):
        """Test contact analytics endpoint"""
        mock_analytics = {
            "total_contacts": 50,
            "contacts_this_month": 5,
            "top_social_platforms": [
                {"platform": "twitter", "count": 20},
                {"platform": "linkedin", "count": 15}
            ],
            "top_locations": [
                {"location": "Coffee Shop", "count": 10},
                {"location": "Tech Conference", "count": 8}
            ],
            "device_sync_stats": {
                "synced": 30,
                "pending": 5,
                "failed": 2,
                "disabled": 13
            }
        }
        
        with patch('app.db.crud.get_contact_analytics') as mock_get_analytics:
            mock_get_analytics.return_value = mock_analytics
            
            response = client.get("/api/v1/contacts/analytics")
            
            assert response.status_code == 200
            data = response.json()
            assert data["total_contacts"] == 50
            assert data["contacts_this_month"] == 5
            assert len(data["top_social_platforms"]) == 2
            assert len(data["top_locations"]) == 2


class TestBulkOperations:
    """Test bulk contact operations"""
    
    def test_bulk_export_vcf(self, client):
        """Test bulk VCF export"""
        export_data = {
            "contact_ids": ["contact_1", "contact_2"],
            "format": "vcf",
            "include_images": False
        }
        
        with patch('app.db.crud.get_contacts_by_ids') as mock_get, \
             patch('app.utils.vcf_generator.generate_bulk_vcf_content') as mock_bulk_vcf:
            
            mock_contacts = [Mock(), Mock()]
            mock_get.return_value = mock_contacts
            mock_bulk_vcf.return_value = "VCF_CONTENT"
            
            response = client.post("/api/v1/contacts/bulk-export", json=export_data)
            
            assert response.status_code == 200
            assert response.headers["content-type"] == "text/vcard; charset=utf-8"
    
    def test_bulk_export_json(self, client):
        """Test bulk JSON export"""
        export_data = {
            "format": "json",
            "include_images": True
        }
        
        with patch('app.db.crud.get_user_contacts') as mock_get:
            mock_contact = Mock()
            mock_contact.id = "contact_1"
            mock_contact.name = "John Doe"
            mock_contact.phone = "+1234567890"
            mock_contact.email = "<EMAIL>"
            mock_contact.location = "Coffee Shop"
            mock_contact.met_at = "Event"
            mock_contact.social_media = {}
            mock_contact.memory_prompt = "Test"
            mock_contact.image_path = "https://example.com/image.jpg"
            mock_contact.created_at = None
            mock_contact.updated_at = None
            mock_get.return_value = [mock_contact]
            
            response = client.post("/api/v1/contacts/bulk-export", json=export_data)
            
            assert response.status_code == 200
            assert response.headers["content-type"] == "application/json; charset=utf-8"
    
    def test_bulk_export_too_many_contacts(self, client):
        """Test bulk export with too many contacts"""
        export_data = {
            "contact_ids": ["contact_" + str(i) for i in range(101)],  # 101 contacts
            "format": "vcf"
        }
        
        response = client.post("/api/v1/contacts/bulk-export", json=export_data)
        
        assert response.status_code == 400
        assert "Cannot export more than 100 contacts" in response.json()["detail"]


class TestSocialPlatforms:
    """Test social media platform functionality"""
    
    def test_get_social_platforms(self, client):
        """Test getting all social platforms"""
        with patch('app.db.crud.get_social_platforms') as mock_get:
            mock_platforms = [
                Mock(name="twitter", display_name="Twitter", is_active=True),
                Mock(name="instagram", display_name="Instagram", is_active=True)
            ]
            mock_get.return_value = mock_platforms
            
            response = client.get("/api/v1/social-platforms/")
            
            assert response.status_code == 200
            data = response.json()
            assert "platforms" in data
    
    def test_validate_social_username(self, client):
        """Test social username validation"""
        platform_name = "twitter"
        username = "johndoe"
        
        with patch('app.db.crud.get_social_platform_by_name') as mock_get:
            mock_platform = Mock()
            mock_platform.name = "twitter"
            mock_platform.display_name = "Twitter"
            mock_platform.is_active = True
            mock_platform.username_validation = "^[a-zA-Z0-9_]{1,15}$"
            mock_platform.url_pattern = "https://twitter.com/{username}"
            mock_platform.username_placeholder = "@username"
            mock_get.return_value = mock_platform
            
            response = client.post(
                f"/api/v1/social-platforms/{platform_name}/validate",
                params={"username": username}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["platform"] == platform_name
            assert data["username"] == username
            assert data["is_valid"] is True
            assert data["profile_url"] == f"https://twitter.com/{username}"


if __name__ == "__main__":
    pytest.main([__file__])
