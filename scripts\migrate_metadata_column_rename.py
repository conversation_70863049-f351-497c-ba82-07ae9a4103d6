#!/usr/bin/env python3
"""
Database migration script to rename 'metadata' column to 'item_metadata' in inbox_items table.

This fixes the SQLAlchemy conflict with the reserved 'metadata' attribute.
"""

import os
import sys
from dotenv import load_dotenv
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

def get_database_connection():
    """Get database connection from environment variables."""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError("DATABASE_URL environment variable not set")
    
    # Parse the database URL
    if database_url.startswith("postgresql://"):
        database_url = database_url.replace("postgresql://", "postgres://")
    
    return psycopg2.connect(database_url)

def migrate_metadata_column():
    """Rename metadata column to item_metadata."""
    conn = None
    cursor = None
    
    try:
        print("Connecting to database...")
        conn = get_database_connection()
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        print("Starting metadata column migration...")
        
        # Check if the old column exists
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'inbox_items' 
            AND column_name = 'metadata'
        """)
        
        if cursor.fetchone():
            print("Found 'metadata' column, renaming to 'item_metadata'...")
            
            # Rename the column
            cursor.execute("""
                ALTER TABLE inbox_items 
                RENAME COLUMN metadata TO item_metadata
            """)
            
            print("✅ Successfully renamed 'metadata' column to 'item_metadata'")
        else:
            print("ℹ️  'metadata' column not found, checking for 'item_metadata'...")
            
            # Check if item_metadata already exists
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'inbox_items' 
                AND column_name = 'item_metadata'
            """)
            
            if cursor.fetchone():
                print("✅ 'item_metadata' column already exists, migration not needed")
            else:
                print("⚠️  Neither 'metadata' nor 'item_metadata' column found")
        
        # Verify the final state
        cursor.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'inbox_items' 
            AND column_name IN ('metadata', 'item_metadata')
            ORDER BY column_name
        """)
        
        columns = cursor.fetchall()
        print("\nFinal column state:")
        for column_name, data_type in columns:
            print(f"  - {column_name}: {data_type}")
        
        print("\n✅ Metadata column migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        if conn:
            conn.rollback()
        raise
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def verify_migration():
    """Verify that the migration was successful."""
    conn = None
    cursor = None
    
    try:
        print("\nVerifying migration...")
        conn = get_database_connection()
        cursor = conn.cursor()
        
        # Check table structure
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'inbox_items'
            AND column_name LIKE '%metadata%'
            ORDER BY column_name
        """)
        
        columns = cursor.fetchall()
        
        if not columns:
            print("⚠️  No metadata columns found")
            return False
        
        print("Metadata columns found:")
        for column_name, data_type, is_nullable in columns:
            print(f"  - {column_name}: {data_type} (nullable: {is_nullable})")
        
        # Check if we have the correct column
        has_item_metadata = any(col[0] == 'item_metadata' for col in columns)
        has_old_metadata = any(col[0] == 'metadata' for col in columns)
        
        if has_item_metadata and not has_old_metadata:
            print("✅ Migration verification successful!")
            return True
        elif has_old_metadata:
            print("❌ Old 'metadata' column still exists")
            return False
        else:
            print("❌ 'item_metadata' column not found")
            return False
    
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def main():
    """Main migration function."""
    print("=== Smart Capture Metadata Column Migration ===")
    print("This script renames 'metadata' to 'item_metadata' in inbox_items table")
    print("to fix SQLAlchemy reserved attribute conflict.\n")
    
    try:
        # Run migration
        migrate_metadata_column()
        
        # Verify migration
        if verify_migration():
            print("\n🎉 Migration completed successfully!")
            return 0
        else:
            print("\n❌ Migration verification failed!")
            return 1
    
    except Exception as e:
        print(f"\n💥 Migration failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
