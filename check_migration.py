#!/usr/bin/env python3
"""Check if social_platforms table exists and has data"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.database import engine
from sqlalchemy import text

def check_social_platforms():
    try:
        with engine.connect() as conn:
            # Check if table exists
            result = conn.execute(text(
                "SELECT table_name FROM information_schema.tables "
                "WHERE table_schema = 'public' AND table_name = 'social_platforms';"
            ))
            table_exists = result.fetchone() is not None
            print(f"social_platforms table exists: {table_exists}")

            if table_exists:
                # Check table structure
                result = conn.execute(text(
                    "SELECT column_name, column_default, is_nullable "
                    "FROM information_schema.columns "
                    "WHERE table_name = 'social_platforms' AND column_name = 'id';"
                ))
                id_column = result.fetchone()
                if id_column:
                    print(f"ID column default: {id_column[1]}")
                    print(f"ID column nullable: {id_column[2]}")

                # Check data count
                result = conn.execute(text("SELECT COUNT(*) FROM social_platforms;"))
                count = result.fetchone()[0]
                print(f"Records in social_platforms: {count}")

                # Show sample data
                if count > 0:
                    result = conn.execute(text("SELECT name, display_name FROM social_platforms LIMIT 3;"))
                    print("Sample platforms:")
                    for row in result:
                        print(f"  - {row[0]}: {row[1]}")

    except Exception as e:
        print(f"Error checking database: {e}")

if __name__ == "__main__":
    check_social_platforms()
