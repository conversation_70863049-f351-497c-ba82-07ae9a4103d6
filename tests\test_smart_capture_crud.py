"""
Unit tests for Smart Capture CRUD operations.

Tests all enhanced database operations for Smart Capture functionality.
"""

import pytest
from sqlalchemy.orm import Session
from app.db.database import SessionLocal, engine
from app.db import crud
from app.db.models import Base, InboxItem
from datetime import datetime, timedelta
import uuid

# Test database setup
@pytest.fixture(scope="function")
def db_session():
    """Create a test database session."""
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    session = SessionLocal()
    
    yield session
    
    # Cleanup
    session.close()
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def test_user_id():
    """Generate a test user ID."""
    return f"test_user_{uuid.uuid4().hex[:8]}"

@pytest.fixture
def sample_inbox_item_data():
    """Sample data for creating inbox items."""
    return {
        "original_url": "https://example.com/test-article",
        "tags": ["technology", "ai", "testing"],
        "category": "technology",
        "title": "Test Article Title",
        "author": "Test Author",
        "content_type": "article",
        "word_count": 500,
        "reading_time": 3
    }

class TestSmartCaptureCRUD:
    """Test Smart Capture CRUD operations."""
    
    def test_create_inbox_item_with_smart_capture_fields(self, db_session: Session, test_user_id: str, sample_inbox_item_data: dict):
        """Test creating inbox item with Smart Capture fields."""
        # Create inbox item
        item = crud.create_inbox_item(
            db=db_session,
            user_id=test_user_id,
            original_url=sample_inbox_item_data["original_url"],
            tags=sample_inbox_item_data["tags"],
            category=sample_inbox_item_data["category"]
        )
        
        # Verify creation
        assert item is not None
        assert item.owner_id == test_user_id
        assert item.original_url == sample_inbox_item_data["original_url"]
        assert item.tags == sample_inbox_item_data["tags"]
        assert item.category == sample_inbox_item_data["category"]
        assert item.status == "pending"
        assert item.created_at is not None
        assert item.updated_at is not None
    
    def test_update_inbox_item_enhanced(self, db_session: Session, test_user_id: str, sample_inbox_item_data: dict):
        """Test enhanced inbox item updates."""
        # Create initial item
        item = crud.create_inbox_item(
            db=db_session,
            user_id=test_user_id,
            original_url=sample_inbox_item_data["original_url"]
        )
        
        # Update with enhanced fields
        updated_item = crud.update_inbox_item_enhanced(
            db=db_session,
            item_id=item.id,
            user_id=test_user_id,
            title=sample_inbox_item_data["title"],
            author=sample_inbox_item_data["author"],
            tags=sample_inbox_item_data["tags"],
            category=sample_inbox_item_data["category"],
            content_type=sample_inbox_item_data["content_type"],
            word_count=sample_inbox_item_data["word_count"],
            reading_time=sample_inbox_item_data["reading_time"]
        )
        
        # Verify updates
        assert updated_item is not None
        assert updated_item.title == sample_inbox_item_data["title"]
        assert updated_item.author == sample_inbox_item_data["author"]
        assert updated_item.tags == sample_inbox_item_data["tags"]
        assert updated_item.category == sample_inbox_item_data["category"]
        assert updated_item.content_type == sample_inbox_item_data["content_type"]
        assert updated_item.word_count == sample_inbox_item_data["word_count"]
        assert updated_item.reading_time == sample_inbox_item_data["reading_time"]
    
    def test_search_inbox_items(self, db_session: Session, test_user_id: str):
        """Test advanced search functionality."""
        # Create test items
        test_items = [
            {
                "url": "https://example.com/ai-article",
                "tags": ["ai", "technology"],
                "category": "technology",
                "title": "AI Revolution"
            },
            {
                "url": "https://example.com/business-news",
                "tags": ["business", "finance"],
                "category": "business",
                "title": "Market Update"
            },
            {
                "url": "https://example.com/tech-review",
                "tags": ["technology", "review"],
                "category": "technology",
                "title": "Tech Product Review"
            }
        ]
        
        created_items = []
        for item_data in test_items:
            item = crud.create_inbox_item(
                db=db_session,
                user_id=test_user_id,
                original_url=item_data["url"],
                tags=item_data["tags"],
                category=item_data["category"]
            )
            # Update with title
            crud.update_inbox_item_enhanced(
                db=db_session,
                item_id=item.id,
                user_id=test_user_id,
                title=item_data["title"]
            )
            created_items.append(item)
        
        # Test search by query
        results = crud.search_inbox_items(
            db=db_session,
            user_id=test_user_id,
            query="AI",
            limit=10,
            skip=0
        )
        assert len(results) >= 1
        assert any("AI" in item.title for item in results)
        
        # Test search by tags
        results = crud.search_inbox_items(
            db=db_session,
            user_id=test_user_id,
            tags=["technology"],
            limit=10,
            skip=0
        )
        assert len(results) >= 2  # Should find 2 technology items
        
        # Test search by category
        results = crud.search_inbox_items(
            db=db_session,
            user_id=test_user_id,
            category="business",
            limit=10,
            skip=0
        )
        assert len(results) == 1
        assert results[0].category == "business"
    
    def test_bulk_operations(self, db_session: Session, test_user_id: str):
        """Test bulk operations functionality."""
        # Create test items
        items = []
        for i in range(5):
            item = crud.create_inbox_item(
                db=db_session,
                user_id=test_user_id,
                original_url=f"https://example.com/bulk-test-{i}",
                tags=["bulk", "test"],
                category="test"
            )
            items.append(item)
        
        item_ids = [item.id for item in items]
        
        # Test bulk delete
        deleted_count = crud.bulk_delete_inbox_items(
            db=db_session,
            user_id=test_user_id,
            item_ids=item_ids[:3]  # Delete first 3
        )
        assert deleted_count == 3
        
        # Verify deletion
        remaining_items = crud.get_user_inbox_items(
            db=db_session,
            user_id=test_user_id,
            skip=0,
            limit=10
        )
        assert len(remaining_items) == 2
        
        # Test bulk categorize
        remaining_ids = [item.id for item in remaining_items]
        updated_count = crud.bulk_update_category(
            db=db_session,
            user_id=test_user_id,
            item_ids=remaining_ids,
            category="updated_category"
        )
        assert updated_count == 2
        
        # Verify category update
        for item_id in remaining_ids:
            item = crud.get_inbox_item_by_id(db=db_session, item_id=item_id, user_id=test_user_id)
            assert item.category == "updated_category"
    
    def test_get_available_tags_and_categories(self, db_session: Session, test_user_id: str):
        """Test getting available tags and categories."""
        # Create items with various tags and categories
        test_data = [
            {"tags": ["python", "programming"], "category": "technology"},
            {"tags": ["javascript", "web"], "category": "technology"},
            {"tags": ["finance", "investing"], "category": "business"},
            {"tags": ["health", "wellness"], "category": "lifestyle"}
        ]
        
        for data in test_data:
            crud.create_inbox_item(
                db=db_session,
                user_id=test_user_id,
                original_url=f"https://example.com/{data['category']}",
                tags=data["tags"],
                category=data["category"]
            )
        
        # Test get available tags
        tags = crud.get_available_tags(db=db_session, user_id=test_user_id)
        expected_tags = {"python", "programming", "javascript", "web", "finance", "investing", "health", "wellness"}
        assert set(tags) == expected_tags
        
        # Test get available categories
        categories = crud.get_available_categories(db=db_session, user_id=test_user_id)
        expected_categories = {"technology", "business", "lifestyle"}
        assert set(categories) == expected_categories
    
    def test_get_inbox_item_count(self, db_session: Session, test_user_id: str):
        """Test getting inbox item count with filters."""
        # Create test items
        for i in range(10):
            crud.create_inbox_item(
                db=db_session,
                user_id=test_user_id,
                original_url=f"https://example.com/count-test-{i}",
                tags=["count", "test"] if i % 2 == 0 else ["count"],
                category="technology" if i < 5 else "business"
            )
        
        # Test total count
        total_count = crud.get_inbox_item_count(
            db=db_session,
            user_id=test_user_id
        )
        assert total_count == 10
        
        # Test count with category filter
        tech_count = crud.get_inbox_item_count(
            db=db_session,
            user_id=test_user_id,
            category="technology"
        )
        assert tech_count == 5
        
        # Test count with tags filter
        test_tag_count = crud.get_inbox_item_count(
            db=db_session,
            user_id=test_user_id,
            tags=["test"]
        )
        assert test_tag_count == 5  # Only even-numbered items have "test" tag
    
    def test_error_handling_and_retry_count(self, db_session: Session, test_user_id: str):
        """Test error handling and retry count functionality."""
        # Create item
        item = crud.create_inbox_item(
            db=db_session,
            user_id=test_user_id,
            original_url="https://example.com/error-test"
        )
        
        # Simulate processing error
        error_message = "Test processing error"
        updated_item = crud.update_inbox_item_enhanced(
            db=db_session,
            item_id=item.id,
            user_id=test_user_id,
            processing_error=error_message,
            retry_count=1
        )
        
        # Verify error tracking
        assert updated_item.processing_error == error_message
        assert updated_item.retry_count == 1
        
        # Simulate another retry
        updated_item = crud.update_inbox_item_enhanced(
            db=db_session,
            item_id=item.id,
            user_id=test_user_id,
            retry_count=2
        )
        
        assert updated_item.retry_count == 2

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
