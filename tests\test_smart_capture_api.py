"""
Integration tests for Smart Capture API endpoints.

Tests all API endpoints for Smart Capture functionality with real HTTP requests.
"""

import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.db.database import SessionLocal, engine
from app.db.models import Base
from app.db import crud
import json
import uuid

client = TestClient(app)

# Mock authentication for testing
def mock_get_current_user():
    return {"uid": "test_user_123", "email": "<EMAIL>"}

# Override the dependency
from app.api.auth import get_current_user
app.dependency_overrides[get_current_user] = mock_get_current_user

@pytest.fixture(scope="function")
def test_db():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def auth_headers():
    """Authentication headers for API requests."""
    return {"Authorization": "Bearer test_token"}

class TestSmartCaptureAPI:
    """Integration tests for Smart Capture API."""
    
    def test_capture_url_with_smart_capture_fields(self, test_db, auth_headers):
        """Test URL capture with Smart Capture fields."""
        payload = {
            "original_url": "https://example.com/test-article",
            "tags": ["technology", "ai"],
            "category": "technology"
        }
        
        response = client.post(
            "/api/v1/inbox/capture",
            json=payload,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["original_url"] == payload["original_url"]
        assert data["tags"] == payload["tags"]
        assert data["category"] == payload["category"]
        assert data["status"] == "pending"
        assert "id" in data
        assert "created_at" in data
    
    def test_search_inbox_items_endpoint(self, test_db, auth_headers):
        """Test search endpoint with various filters."""
        # Create test items first
        test_items = [
            {
                "original_url": "https://example.com/ai-article",
                "tags": ["ai", "technology"],
                "category": "technology"
            },
            {
                "original_url": "https://example.com/business-news",
                "tags": ["business", "finance"],
                "category": "business"
            }
        ]
        
        created_items = []
        for item in test_items:
            response = client.post(
                "/api/v1/inbox/capture",
                json=item,
                headers=auth_headers
            )
            assert response.status_code == 201
            created_items.append(response.json())
        
        # Test search by query
        response = client.get(
            "/api/v1/inbox/search?query=ai&limit=10",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "available_tags" in data
        assert "available_categories" in data
        
        # Test search by tags
        response = client.get(
            "/api/v1/inbox/search?tags=technology&limit=10",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) >= 1
        
        # Test search by category
        response = client.get(
            "/api/v1/inbox/search?category=business&limit=10",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) >= 1
    
    def test_bulk_delete_endpoint(self, test_db, auth_headers):
        """Test bulk delete endpoint."""
        # Create test items
        items = []
        for i in range(3):
            response = client.post(
                "/api/v1/inbox/capture",
                json={
                    "original_url": f"https://example.com/bulk-delete-{i}",
                    "tags": ["bulk", "test"],
                    "category": "test"
                },
                headers=auth_headers
            )
            assert response.status_code == 201
            items.append(response.json())
        
        # Test bulk delete
        item_ids = [item["id"] for item in items]
        response = client.post(
            "/api/v1/inbox/bulk-delete",
            json={"item_ids": item_ids},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["processed_count"] == 3
        assert data["failed_count"] == 0
    
    def test_bulk_categorize_endpoint(self, test_db, auth_headers):
        """Test bulk categorize endpoint."""
        # Create test items
        items = []
        for i in range(2):
            response = client.post(
                "/api/v1/inbox/capture",
                json={
                    "original_url": f"https://example.com/bulk-categorize-{i}",
                    "tags": ["bulk", "test"],
                    "category": "original"
                },
                headers=auth_headers
            )
            assert response.status_code == 201
            items.append(response.json())
        
        # Test bulk categorize
        item_ids = [item["id"] for item in items]
        response = client.post(
            "/api/v1/inbox/bulk-categorize",
            json={
                "item_ids": item_ids,
                "category": "updated_category"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["processed_count"] == 2
        assert "updated_category" in data["message"]
    
    def test_bulk_tag_endpoint(self, test_db, auth_headers):
        """Test bulk tag operations."""
        # Create test item
        response = client.post(
            "/api/v1/inbox/capture",
            json={
                "original_url": "https://example.com/bulk-tag-test",
                "tags": ["original"],
                "category": "test"
            },
            headers=auth_headers
        )
        assert response.status_code == 201
        item = response.json()
        
        # Test bulk add tags
        response = client.post(
            "/api/v1/inbox/bulk-tag",
            json={
                "item_ids": [item["id"]],
                "tags": ["new_tag", "another_tag"],
                "operation": "add"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["processed_count"] == 1
        assert "added tags" in data["message"]
        
        # Test bulk remove tags
        response = client.post(
            "/api/v1/inbox/bulk-tag",
            json={
                "item_ids": [item["id"]],
                "tags": ["original"],
                "operation": "remove"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "removed tags" in data["message"]
    
    def test_update_inbox_item_endpoint(self, test_db, auth_headers):
        """Test update inbox item endpoint."""
        # Create test item
        response = client.post(
            "/api/v1/inbox/capture",
            json={
                "original_url": "https://example.com/update-test",
                "tags": ["original"],
                "category": "original"
            },
            headers=auth_headers
        )
        assert response.status_code == 201
        item = response.json()
        
        # Test update
        update_data = {
            "title": "Updated Title",
            "tags": ["updated", "tags"],
            "category": "updated_category"
        }
        
        response = client.put(
            f"/api/v1/inbox/{item['id']}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        updated_item = response.json()
        assert updated_item["title"] == update_data["title"]
        assert updated_item["tags"] == update_data["tags"]
        assert updated_item["category"] == update_data["category"]
    
    def test_add_to_notes_endpoint(self, test_db, auth_headers):
        """Test add to notes integration endpoint."""
        # Create test item with content
        response = client.post(
            "/api/v1/inbox/capture",
            json={
                "original_url": "https://example.com/notes-test",
                "tags": ["notes", "test"],
                "category": "test"
            },
            headers=auth_headers
        )
        assert response.status_code == 201
        item = response.json()
        
        # Test add to notes
        response = client.post(
            f"/api/v1/inbox/{item['id']}/add-to-notes",
            json={
                "note_title": "Test Note",
                "note_tags": ["imported", "from_inbox"]
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "note_id" in data
        assert "Test Note" in data["message"]
    
    def test_chat_context_endpoint(self, test_db, auth_headers):
        """Test chat context preparation endpoint."""
        # Create test item
        response = client.post(
            "/api/v1/inbox/capture",
            json={
                "original_url": "https://example.com/chat-test",
                "tags": ["chat", "test"],
                "category": "test"
            },
            headers=auth_headers
        )
        assert response.status_code == 201
        item = response.json()
        
        # Test chat context preparation
        response = client.post(
            f"/api/v1/inbox/{item['id']}/chat-context",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "context_id" in data
        assert data["context_id"].startswith("inbox_")
    
    def test_get_available_tags_endpoint(self, test_db, auth_headers):
        """Test get available tags endpoint."""
        # Create items with tags
        for i, tags in enumerate([["python", "programming"], ["javascript", "web"]]):
            client.post(
                "/api/v1/inbox/capture",
                json={
                    "original_url": f"https://example.com/tags-test-{i}",
                    "tags": tags,
                    "category": "technology"
                },
                headers=auth_headers
            )
        
        # Test get tags
        response = client.get("/api/v1/inbox/tags", headers=auth_headers)
        assert response.status_code == 200
        tags = response.json()
        assert isinstance(tags, list)
        assert "python" in tags
        assert "javascript" in tags
    
    def test_get_available_categories_endpoint(self, test_db, auth_headers):
        """Test get available categories endpoint."""
        # Create items with categories
        categories = ["technology", "business", "science"]
        for i, category in enumerate(categories):
            client.post(
                "/api/v1/inbox/capture",
                json={
                    "original_url": f"https://example.com/categories-test-{i}",
                    "tags": ["test"],
                    "category": category
                },
                headers=auth_headers
            )
        
        # Test get categories
        response = client.get("/api/v1/inbox/categories", headers=auth_headers)
        assert response.status_code == 200
        returned_categories = response.json()
        assert isinstance(returned_categories, list)
        for category in categories:
            assert category in returned_categories
    
    def test_error_handling(self, test_db, auth_headers):
        """Test API error handling."""
        # Test invalid item ID
        response = client.get(
            "/api/v1/inbox/invalid-id",
            headers=auth_headers
        )
        assert response.status_code == 404
        
        # Test bulk operation with empty list
        response = client.post(
            "/api/v1/inbox/bulk-delete",
            json={"item_ids": []},
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error
        
        # Test invalid search parameters
        response = client.get(
            "/api/v1/inbox/search?date_from=invalid-date",
            headers=auth_headers
        )
        assert response.status_code == 400

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
