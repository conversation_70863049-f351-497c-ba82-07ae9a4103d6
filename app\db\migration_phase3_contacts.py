"""
Phase 3 Contact Management Database Migration

This migration adds the required fields and tables for Phase 3 contact management
functionality including device sync, image support, and social platform configuration.
"""

from sqlalchemy import text
from app.db.database import engine
import logging
import sys

# Configure logging to work properly when run as module
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def run_migration():
    """
    Apply Phase 3 contact management database migration.
    
    This migration:
    1. Adds missing fields to contacts table
    2. Creates social_platforms table
    3. Adds proper indexes for performance
    4. Adds constraints for data integrity
    """
    
    print("🚀 Starting Phase 3 Contact Management Migration...")
    logger.info("Starting Phase 3 Contact Management Migration...")
    
    migration_sql = """
    -- Add missing fields to contacts table
    DO $$ 
    BEGIN
        -- Add email field if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'contacts' AND column_name = 'email') THEN
            ALTER TABLE contacts ADD COLUMN email VARCHAR(255);
        END IF;
        
        -- Add location field if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'contacts' AND column_name = 'location') THEN
            ALTER TABLE contacts ADD COLUMN location VARCHAR(200);
        END IF;
        
        -- Add image_path field if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'contacts' AND column_name = 'image_path') THEN
            ALTER TABLE contacts ADD COLUMN image_path VARCHAR(500);
        END IF;
        
        -- Add image_public_id field if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'contacts' AND column_name = 'image_public_id') THEN
            ALTER TABLE contacts ADD COLUMN image_public_id VARCHAR(100);
        END IF;
        
        -- Add device_sync_status field if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'contacts' AND column_name = 'device_sync_status') THEN
            ALTER TABLE contacts ADD COLUMN device_sync_status VARCHAR(20) DEFAULT 'disabled';
        END IF;
        
        -- Add device_contact_id field if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'contacts' AND column_name = 'device_contact_id') THEN
            ALTER TABLE contacts ADD COLUMN device_contact_id VARCHAR(100);
        END IF;
        
        -- Add synced_at field if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'contacts' AND column_name = 'synced_at') THEN
            ALTER TABLE contacts ADD COLUMN synced_at TIMESTAMP WITH TIME ZONE;
        END IF;
        
        -- Add deleted_at field if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'contacts' AND column_name = 'deleted_at') THEN
            ALTER TABLE contacts ADD COLUMN deleted_at TIMESTAMP WITH TIME ZONE;
        END IF;
    END $$;
    
    -- Handle social_platforms table creation with proper DEFAULT handling
    DO $$
    BEGIN
        -- Check if table exists
        IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'social_platforms') THEN
            -- Create table with proper structure
            CREATE TABLE social_platforms (
                id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR NOT NULL UNIQUE,
                display_name VARCHAR NOT NULL,
                url_pattern VARCHAR NOT NULL,
                app_scheme VARCHAR,
                username_validation VARCHAR,
                username_placeholder VARCHAR,
                color VARCHAR,
                icon_url VARCHAR,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE
            );
        ELSE
            -- Table exists, ensure id column has DEFAULT
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'social_platforms' 
                AND column_name = 'id' 
                AND column_default IS NOT NULL
            ) THEN
                -- Add DEFAULT to existing id column
                ALTER TABLE social_platforms ALTER COLUMN id SET DEFAULT gen_random_uuid();
            END IF;
        END IF;
    END $$;
    
    -- Create indexes for performance (only if they don't exist)
    CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email) WHERE deleted_at IS NULL;
    CREATE INDEX IF NOT EXISTS idx_contacts_phone_active ON contacts(phone) WHERE deleted_at IS NULL;
    CREATE INDEX IF NOT EXISTS idx_contacts_location ON contacts(location) WHERE deleted_at IS NULL;
    CREATE INDEX IF NOT EXISTS idx_contacts_device_sync ON contacts(device_sync_status) WHERE deleted_at IS NULL;
    CREATE INDEX IF NOT EXISTS idx_contacts_deleted_at ON contacts(deleted_at);
    CREATE INDEX IF NOT EXISTS idx_contacts_owner_active ON contacts(owner_id) WHERE deleted_at IS NULL;
    CREATE INDEX IF NOT EXISTS idx_social_platforms_name ON social_platforms(name);
    CREATE INDEX IF NOT EXISTS idx_social_platforms_active ON social_platforms(is_active) WHERE is_active = TRUE;
    
    -- Add constraints for data integrity
    DO $$
    BEGIN
        -- Add device sync status constraint if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints 
                      WHERE constraint_name = 'valid_sync_status') THEN
            ALTER TABLE contacts ADD CONSTRAINT valid_sync_status 
                CHECK (device_sync_status IN ('disabled', 'pending', 'synced', 'failed'));
        END IF;
        
        -- Add email validation constraint if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints 
                      WHERE constraint_name = 'valid_email') THEN
            ALTER TABLE contacts ADD CONSTRAINT valid_email 
                CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$');
        END IF;
    EXCEPTION
        WHEN duplicate_object THEN
            -- Constraints already exist, continue
            NULL;
    END $$;
    
    -- Insert default social platforms (only if they don't exist)
    INSERT INTO social_platforms (name, display_name, url_pattern, app_scheme, username_validation, username_placeholder, color, is_active)
    VALUES 
        ('twitter', 'Twitter', 'https://twitter.com/{username}', 'twitter://user?screen_name={username}', '^[a-zA-Z0-9_]{1,15}$', '@username', '#1DA1F2', TRUE),
        ('instagram', 'Instagram', 'https://instagram.com/{username}', 'instagram://user?username={username}', '^[a-zA-Z0-9_.]{1,30}$', '@username', '#E4405F', TRUE),
        ('linkedin', 'LinkedIn', 'https://linkedin.com/in/{username}', 'linkedin://profile/{username}', '^[a-zA-Z0-9-]{3,100}$', 'username', '#0077B5', TRUE),
        ('facebook', 'Facebook', 'https://facebook.com/{username}', 'fb://profile/{username}', '^[a-zA-Z0-9.]{5,50}$', 'username', '#1877F2', TRUE),
        ('tiktok', 'TikTok', 'https://tiktok.com/@{username}', 'tiktok://user?username={username}', '^[a-zA-Z0-9_.]{2,24}$', '@username', '#000000', TRUE),
        ('youtube', 'YouTube', 'https://youtube.com/@{username}', 'youtube://channel/{username}', '^[a-zA-Z0-9_-]{3,30}$', '@username', '#FF0000', TRUE),
        ('snapchat', 'Snapchat', 'https://snapchat.com/add/{username}', 'snapchat://add/{username}', '^[a-zA-Z0-9._-]{3,15}$', 'username', '#FFFC00', TRUE),
        ('discord', 'Discord', 'https://discord.com/users/{username}', 'discord://users/{username}', '^.{2,32}#[0-9]{4}$', 'username#1234', '#5865F2', TRUE),
        ('telegram', 'Telegram', 'https://t.me/{username}', 'tg://resolve?domain={username}', '^[a-zA-Z0-9_]{5,32}$', '@username', '#0088CC', TRUE),
        ('whatsapp', 'WhatsApp', 'https://wa.me/{username}', 'whatsapp://send?phone={username}', '^\\+[1-9]\\d{1,14}$', '+1234567890', '#25D366', TRUE)
    ON CONFLICT (name) DO NOTHING;
    """
    
    try:
        with engine.connect() as connection:
            # Execute the migration in a transaction
            with connection.begin():
                print("⚡ Executing database changes...")
                logger.info("Executing migration SQL...")
                connection.execute(text(migration_sql))
                logger.info("Phase 3 contact management migration completed successfully")
                print("✅ Phase 3 contact management migration completed successfully!")
                
                # Verify tables were created/updated
                print("🔍 Verifying migration results...")
                
                # Check contacts table columns
                result = connection.execute(text("""
                    SELECT count(*) FROM information_schema.columns 
                    WHERE table_name = 'contacts' AND column_name IN 
                    ('email', 'location', 'image_path', 'device_sync_status')
                """))
                contacts_new_cols = result.scalar()
                print(f"   📋 Contacts table: {contacts_new_cols}/4 new columns added")
                
                # Check social_platforms table
                result = connection.execute(text("""
                    SELECT count(*) FROM information_schema.tables 
                    WHERE table_name = 'social_platforms'
                """))
                social_table_exists = result.scalar()
                print(f"   🌐 Social platforms table: {'✅ Created' if social_table_exists else '❌ Missing'}")
                
                # Check if default platforms were inserted
                result = connection.execute(text("SELECT count(*) FROM social_platforms"))
                platform_count = result.scalar()
                print(f"   📱 Default social platforms: {platform_count} entries")
                
                print("🎉 Migration verification complete!")
                
    except Exception as e:
        logger.error(f"Phase 3 contact management migration failed: {e}")
        print(f"❌ Migration failed: {e}")
        raise

if __name__ == "__main__":
    print("🔧 Phase 3 Contact Management Database Migration")
    print("=" * 50)
    try:
        run_migration()
        print("=" * 50)
        print("🎯 All migration tasks completed successfully!")
    except Exception as e:
        print("=" * 50)
        print(f"💥 Migration failed with error: {e}")
        sys.exit(1)
