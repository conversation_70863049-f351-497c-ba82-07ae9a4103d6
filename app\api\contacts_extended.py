"""
Extended Contact Management API endpoints for Phase 3 functionality.

This module provides additional endpoints for device sync, bulk operations,
social media integration, analytics, and advanced search functionality.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import Response
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
import json
import csv
import io
from datetime import datetime

from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import (
    SocialPlatformsResponse,
    DeviceSyncRequest,
    DeviceSyncResponse,
    BulkDeviceSyncRequest,
    BulkDeviceSyncResponse,
    ContactExportRequest,
    ContactSearchRequest,
    ContactSearchResponse,
    ContactAnalyticsResponse,
    SocialVerificationRequest,
    SocialVerificationResponse
)
from app.utils.vcf_generator import generate_bulk_vcf_content
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/contacts", tags=["Contacts Extended"])

@router.get("/search", response_model=ContactSearchResponse)
async def search_contacts(
    q: str = Query(..., min_length=1, description="Search query"),
    fields: Optional[str] = Query(None, description="Comma-separated fields to search in"),
    platform: Optional[str] = Query(None, description="Filter by social platform"),
    date_from: Optional[str] = Query(None, description="Created after date (ISO format)"),
    date_to: Optional[str] = Query(None, description="Created before date (ISO format)"),
    limit: int = Query(20, ge=1, le=100, description="Number of results to return"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Advanced contact search with filters.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Query Parameters**:
    - **q**: Search query (required)
    - **fields**: Specific fields to search in (optional)
    - **platform**: Filter by social media platform (optional)
    - **date_from**: Filter contacts created after this date (optional)
    - **date_to**: Filter contacts created before this date (optional)
    - **limit**: Number of results to return (1-100, default: 20)
    - **offset**: Number of results to skip (default: 0)
    
    **Returns**:
    - **200 OK**: Search results with metadata
    - **400 Bad Request**: Invalid query parameters
    - **401 Unauthorized**: Invalid authentication token
    """
    try:
        user_id = current_user["uid"]
        
        # Parse fields if provided
        search_fields = None
        if fields:
            search_fields = [field.strip() for field in fields.split(",")]
        
        # Parse dates if provided
        parsed_date_from = None
        parsed_date_to = None
        
        if date_from:
            try:
                parsed_date_from = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date_from format. Use ISO 8601 format."
                )
        
        if date_to:
            try:
                parsed_date_to = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date_to format. Use ISO 8601 format."
                )
        
        # Perform search
        contacts = crud.search_contacts(
            db=db,
            user_id=user_id,
            query=q,
            fields=search_fields,
            platform=platform,
            date_from=parsed_date_from,
            date_to=parsed_date_to,
            skip=offset,
            limit=limit
        )
        
        # Count total results (simplified - could be optimized with separate count query)
        total = len(contacts)
        
        # Build filters dict for response
        filters = {}
        if fields:
            filters["fields"] = search_fields
        if platform:
            filters["platform"] = platform
        if date_from:
            filters["date_from"] = date_from
        if date_to:
            filters["date_to"] = date_to
        
        return ContactSearchResponse(
            results=contacts,
            total=total,
            query=q,
            filters=filters
        )
        
    except Exception as e:
        logger.error(f"Error searching contacts for user {current_user.get('uid')}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search contacts"
        )

@router.get("/analytics", response_model=ContactAnalyticsResponse)
async def get_contact_analytics(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get contact analytics and statistics for the authenticated user.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Returns**:
    - **200 OK**: Contact analytics data
    - **401 Unauthorized**: Invalid authentication token
    """
    try:
        user_id = current_user["uid"]
        analytics = crud.get_contact_analytics(db=db, user_id=user_id)
        
        return ContactAnalyticsResponse(**analytics)
        
    except Exception as e:
        logger.error(f"Error getting contact analytics for user {current_user.get('uid')}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get contact analytics"
        )

@router.post("/{contact_id}/device-sync", response_model=DeviceSyncResponse)
async def update_device_sync_status(
    contact_id: str,
    sync_request: DeviceSyncRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update device sync status for a contact.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Request Body**:
    - **device_contact_id**: Device-specific contact ID (optional)
    - **sync_status**: Sync status (synced, failed, pending)
    - **sync_error**: Error message if sync failed (optional)
    - **device_info**: Device information (optional)
    
    **Returns**:
    - **200 OK**: Updated sync status
    - **404 Not Found**: Contact not found
    - **400 Bad Request**: Invalid sync status
    - **401 Unauthorized**: Invalid authentication token
    """
    try:
        user_id = current_user["uid"]
        
        # Validate sync status
        valid_statuses = ['disabled', 'pending', 'synced', 'failed']
        if sync_request.sync_status not in valid_statuses:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid sync status. Must be one of: {', '.join(valid_statuses)}"
            )
        
        # Update contact sync status
        updated_contact = crud.update_contact_sync_status(
            db=db,
            contact_id=contact_id,
            user_id=user_id,
            sync_status=sync_request.sync_status,
            device_contact_id=sync_request.device_contact_id,
            sync_error=sync_request.sync_error
        )
        
        if not updated_contact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contact not found"
            )
        
        return DeviceSyncResponse(
            contact_id=contact_id,
            sync_status=updated_contact.device_sync_status,
            synced_at=updated_contact.synced_at,
            sync_error=sync_request.sync_error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating device sync for contact {contact_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update device sync status"
        )

@router.post("/bulk-export")
async def bulk_export_contacts(
    export_request: ContactExportRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Export multiple contacts in various formats.
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Request Body**:
    - **contact_ids**: List of contact IDs to export (optional - exports all if not provided)
    - **format**: Export format (vcf, csv, json)
    - **include_images**: Whether to include image URLs in export
    
    **Returns**:
    - **200 OK**: Exported file content
    - **400 Bad Request**: Invalid format or too many contacts
    - **401 Unauthorized**: Invalid authentication token
    """
    try:
        user_id = current_user["uid"]
        
        # Get contacts to export
        if export_request.contact_ids:
            if len(export_request.contact_ids) > 100:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot export more than 100 contacts at once"
                )
            contacts = crud.get_contacts_by_ids(db=db, user_id=user_id, contact_ids=export_request.contact_ids)
        else:
            # Export all contacts
            contacts = crud.get_user_contacts(db=db, user_id=user_id, limit=1000)
        
        if not contacts:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No contacts found to export"
            )
        
        # Generate export content based on format
        if export_request.format.lower() == "vcf":
            content = generate_bulk_vcf_content(contacts)
            media_type = "text/vcard"
            filename = "contacts.vcf"
            
        elif export_request.format.lower() == "json":
            contact_data = []
            for contact in contacts:
                data = {
                    "id": contact.id,
                    "name": contact.name,
                    "phone": contact.phone,
                    "email": contact.email,
                    "location": contact.location,
                    "met_at": contact.met_at,
                    "social_media": contact.social_media,
                    "memory_prompt": contact.memory_prompt,
                    "created_at": contact.created_at.isoformat() if contact.created_at else None,
                    "updated_at": contact.updated_at.isoformat() if contact.updated_at else None
                }
                if export_request.include_images and contact.image_path:
                    data["image_path"] = contact.image_path
                contact_data.append(data)
            
            content = json.dumps(contact_data, indent=2)
            media_type = "application/json"
            filename = "contacts.json"
            
        elif export_request.format.lower() == "csv":
            output = io.StringIO()
            fieldnames = ["name", "phone", "email", "location", "met_at", "memory_prompt", "created_at"]
            if export_request.include_images:
                fieldnames.append("image_path")
            
            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()
            
            for contact in contacts:
                row = {
                    "name": contact.name,
                    "phone": contact.phone or "",
                    "email": contact.email or "",
                    "location": contact.location or "",
                    "met_at": contact.met_at or "",
                    "memory_prompt": contact.memory_prompt or "",
                    "created_at": contact.created_at.isoformat() if contact.created_at else ""
                }
                if export_request.include_images:
                    row["image_path"] = contact.image_path or ""
                writer.writerow(row)
            
            content = output.getvalue()
            media_type = "text/csv"
            filename = "contacts.csv"
            
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid format. Supported formats: vcf, json, csv"
            )
        
        return Response(
            content=content,
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Type": f"{media_type}; charset=utf-8"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting contacts for user {current_user.get('uid')}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export contacts"
        )
