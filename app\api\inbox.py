"""
Frictionless Inbox API endpoints for capturing and processing web content.

This module provides endpoints for users to capture URLs for background processing,
content extraction, and AI-powered summarization.
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import (
    InboxItemCreate, InboxItemResponse, InboxItemListResponse,
    InboxItemUpdate, InboxItemSearchRequest, InboxItemSearchResponse,
    InboxItemBulkDeleteRequest, InboxItemBulkCategorizeRequest,
    InboxItemBulkTagRequest, InboxItemBulkRetryRequest, InboxItemBulkResponse,
    InboxItemAddToNotesRequest, InboxItemAddToNotesResponse,
    InboxItemChatContextResponse
)
import logging
from app.utils.cache import smart_capture_cache, cache_response, invalidate_user_cache_on_update

# Import Celery task (with fallback for development)
try:
    from app.worker import process_inbox_item
    CELERY_AVAILABLE = True
except ImportError:
    CELERY_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("Celery not available, using background tasks fallback")

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/inbox", tags=["Inbox"])

@router.post("/capture", response_model=InboxItemResponse, status_code=status.HTTP_201_CREATED)
@invalidate_user_cache_on_update
def capture_url(
    inbox_item: InboxItemCreate,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Capture a URL for background processing and content extraction.
    
    This endpoint immediately saves the URL to the database with "pending" status
    and triggers a background task to process the content. The user receives
    an immediate response while processing happens asynchronously.
    
    - **original_url**: The URL to capture and process (1-2000 characters)
    
    Returns the created inbox item with generated ID and timestamps.
    """
    try:
        user_id = current_user["uid"]
        
        # Create the inbox item with pending status and Smart Capture fields
        db_item = crud.create_inbox_item(
            db=db,
            user_id=user_id,
            original_url=inbox_item.original_url,
            tags=inbox_item.tags,
            category=inbox_item.category
        )
        
        # Trigger background processing task
        if CELERY_AVAILABLE:
            # Use Celery for production background processing
            process_inbox_item.delay(db_item.id)
            logger.info(f"Celery task queued for inbox item: {db_item.id}")
        else:
            # Fallback to FastAPI background tasks for development
            background_tasks.add_task(process_inbox_item_background, db_item.id)
            logger.info(f"Background task queued for inbox item: {db_item.id}")
        
        logger.info(f"Captured URL for user {user_id}: {inbox_item.original_url}")
        
        return db_item
        
    except Exception as e:
        logger.error(f"Error capturing URL: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to capture URL for processing"
        )

@router.get("/", response_model=InboxItemListResponse)
def get_inbox_items(
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all inbox items for the authenticated user with filtering and pagination.
    
    - **skip**: Number of items to skip (default: 0)
    - **limit**: Maximum number of items to return (default: 100)
    - **status_filter**: Filter by status (pending, processing, complete, failed)
    
    Returns a list of inbox items ordered by creation date (newest first).
    """
    user_id = current_user["uid"]
    
    inbox_items = crud.get_user_inbox_items(
        db=db,
        user_id=user_id,
        skip=skip,
        limit=limit,
        status=status_filter
    )
    
    return InboxItemListResponse(inbox_items=inbox_items, total=len(inbox_items))

@router.get("/{item_id}", response_model=InboxItemResponse)
def get_inbox_item(
    item_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific inbox item by ID"""
    user_id = current_user["uid"]
    
    db_item = crud.get_inbox_item_by_id(db=db, item_id=item_id, user_id=user_id)
    if db_item is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Inbox item not found"
        )
    
    return db_item

@router.delete("/{item_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_inbox_item(
    item_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a specific inbox item"""
    user_id = current_user["uid"]
    
    success = crud.delete_inbox_item(db=db, item_id=item_id, user_id=user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Inbox item not found"
        )
    
    return None

def process_inbox_item_background(item_id: str):
    """
    Background task fallback for processing inbox items when Celery is not available.

    This function provides basic processing functionality for development environments
    where Celery might not be set up. In production, use the Celery task instead.

    Args:
        item_id: The ID of the inbox item to process
    """
    try:
        from app.sdk.jina_reader_sdk import get_content_from_url
        from app.db.database import SessionLocal

        logger.info(f"Background processing (fallback) for inbox item: {item_id}")

        # Get database session
        db = SessionLocal()

        try:
            # Update status to processing
            crud.update_inbox_item_status(db, item_id, "processing")

            # Get the item
            db_item = db.query(crud.InboxItem).filter(crud.InboxItem.id == item_id).first()
            if not db_item:
                logger.error(f"Inbox item not found: {item_id}")
                return

            # Extract content
            content_result = get_content_from_url(db_item.original_url)

            if content_result["success"]:
                # Save content and mark as complete
                crud.update_inbox_item_content(db, item_id, clean_content=content_result["content"])
                crud.update_inbox_item_content(db, item_id, summary="Content extracted successfully (basic processing)")
                crud.update_inbox_item_status(db, item_id, "complete")
                logger.info(f"Successfully processed inbox item: {item_id}")
            else:
                # Mark as failed
                crud.update_inbox_item_status(db, item_id, "failed")
                logger.error(f"Failed to extract content for item {item_id}: {content_result['error']}")

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error in background processing fallback: {e}")
        # Try to mark as failed
        try:
            db = SessionLocal()
            crud.update_inbox_item_status(db, item_id, "failed")
            db.close()
        except:
            pass

# Enhanced Smart Capture API Endpoints

@router.get("/search", response_model=InboxItemSearchResponse)
@cache_response(ttl=300, key_prefix="search")  # Cache for 5 minutes
def search_inbox_items(
    query: Optional[str] = None,
    tags: Optional[str] = None,  # Comma-separated tags
    category: Optional[str] = None,
    content_type: Optional[str] = None,
    status: Optional[str] = None,
    date_from: Optional[str] = None,  # ISO date string
    date_to: Optional[str] = None,    # ISO date string
    sort_by: str = "created_at",
    sort_order: str = "desc",
    page: int = 1,
    limit: int = 20,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Advanced search for inbox items with filtering and sorting.

    - **query**: Search text across title, content, summary, and author
    - **tags**: Comma-separated list of tags to filter by
    - **category**: Filter by content category
    - **content_type**: Filter by content type (article, video, etc.)
    - **status**: Filter by processing status
    - **date_from/date_to**: Date range filtering (ISO format)
    - **sort_by**: Field to sort by (created_at, title, word_count, etc.)
    - **sort_order**: Sort direction (asc/desc)
    - **page**: Page number (1-based)
    - **limit**: Items per page (1-100)
    """
    try:
        user_id = current_user["uid"]

        # Parse tags if provided
        tag_list = None
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]

        # Parse dates if provided
        from datetime import datetime
        date_from_dt = None
        date_to_dt = None
        if date_from:
            try:
                date_from_dt = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date_from format. Use ISO format (YYYY-MM-DDTHH:MM:SS)"
                )
        if date_to:
            try:
                date_to_dt = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date_to format. Use ISO format (YYYY-MM-DDTHH:MM:SS)"
                )

        # Calculate skip for pagination
        skip = (page - 1) * limit

        # Search items
        items = crud.search_inbox_items(
            db=db,
            user_id=user_id,
            query=query,
            tags=tag_list,
            category=category,
            content_type=content_type,
            status=status,
            date_from=date_from_dt,
            date_to=date_to_dt,
            sort_by=sort_by,
            sort_order=sort_order,
            skip=skip,
            limit=limit
        )

        # Get total count
        total = crud.get_inbox_item_count(
            db=db,
            user_id=user_id,
            query=query,
            tags=tag_list,
            category=category,
            content_type=content_type,
            status=status,
            date_from=date_from_dt,
            date_to=date_to_dt
        )

        # Get available tags and categories
        available_tags = crud.get_available_tags(db=db, user_id=user_id)
        available_categories = crud.get_available_categories(db=db, user_id=user_id)

        return InboxItemSearchResponse(
            items=items,
            total=total,
            page=page,
            limit=limit,
            available_tags=available_tags,
            available_categories=available_categories
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error searching inbox items: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search inbox items"
        )

@router.post("/bulk-delete", response_model=InboxItemBulkResponse)
@invalidate_user_cache_on_update
def bulk_delete_inbox_items(
    request: InboxItemBulkDeleteRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Bulk delete multiple inbox items."""
    try:
        user_id = current_user["uid"]

        deleted_count = crud.bulk_delete_inbox_items(
            db=db,
            user_id=user_id,
            item_ids=request.item_ids
        )

        return InboxItemBulkResponse(
            success=True,
            processed_count=deleted_count,
            failed_count=len(request.item_ids) - deleted_count,
            message=f"Successfully deleted {deleted_count} items"
        )

    except Exception as e:
        logger.error(f"Error in bulk delete: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete items"
        )

@router.post("/bulk-categorize", response_model=InboxItemBulkResponse)
def bulk_categorize_inbox_items(
    request: InboxItemBulkCategorizeRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Bulk update category for multiple inbox items."""
    try:
        user_id = current_user["uid"]

        updated_count = crud.bulk_update_category(
            db=db,
            user_id=user_id,
            item_ids=request.item_ids,
            category=request.category
        )

        return InboxItemBulkResponse(
            success=True,
            processed_count=updated_count,
            failed_count=len(request.item_ids) - updated_count,
            message=f"Successfully categorized {updated_count} items as '{request.category}'"
        )

    except Exception as e:
        logger.error(f"Error in bulk categorize: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to categorize items"
        )

@router.post("/bulk-tag", response_model=InboxItemBulkResponse)
def bulk_tag_inbox_items(
    request: InboxItemBulkTagRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Bulk add or remove tags for multiple inbox items."""
    try:
        user_id = current_user["uid"]

        if request.operation == "add":
            updated_count = crud.bulk_add_tags(
                db=db,
                user_id=user_id,
                item_ids=request.item_ids,
                tags=request.tags
            )
            message = f"Successfully added tags to {updated_count} items"
        elif request.operation == "remove":
            updated_count = crud.bulk_remove_tags(
                db=db,
                user_id=user_id,
                item_ids=request.item_ids,
                tags=request.tags
            )
            message = f"Successfully removed tags from {updated_count} items"
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Operation must be 'add' or 'remove'"
            )

        return InboxItemBulkResponse(
            success=True,
            processed_count=updated_count,
            failed_count=len(request.item_ids) - updated_count,
            message=message
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in bulk tag: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update tags"
        )

@router.put("/{item_id}", response_model=InboxItemResponse)
@invalidate_user_cache_on_update
def update_inbox_item(
    item_id: str,
    update_data: InboxItemUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update an inbox item with Smart Capture fields."""
    try:
        user_id = current_user["uid"]

        # Check if item exists and belongs to user
        existing_item = crud.get_inbox_item_by_id(db=db, item_id=item_id, user_id=user_id)
        if not existing_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Inbox item not found"
            )

        # Update the item
        updated_item = crud.update_inbox_item_enhanced(
            db=db,
            item_id=item_id,
            user_id=user_id,
            title=update_data.title,
            tags=update_data.tags,
            category=update_data.category
        )

        if not updated_item:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update inbox item"
            )

        return updated_item

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating inbox item: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update inbox item"
        )

@router.post("/{item_id}/add-to-notes", response_model=InboxItemAddToNotesResponse)
def add_inbox_item_to_notes(
    item_id: str,
    request: InboxItemAddToNotesRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add an inbox item's content to the Notes system."""
    try:
        user_id = current_user["uid"]

        # Get the inbox item
        inbox_item = crud.get_inbox_item_by_id(db=db, item_id=item_id, user_id=user_id)
        if not inbox_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Inbox item not found"
            )

        # Prepare note content
        note_title = request.note_title or inbox_item.title or f"Captured: {inbox_item.original_url}"
        note_content = f"**Source:** {inbox_item.original_url}\n\n"

        if inbox_item.summary:
            note_content += f"**Summary:**\n{inbox_item.summary}\n\n"

        if inbox_item.clean_content:
            note_content += f"**Content:**\n{inbox_item.clean_content}"

        # Combine tags
        note_tags = list(set((request.note_tags or []) + (inbox_item.tags or [])))

        # Create note using the notes CRUD
        from app.db.crud import create_note
        note = create_note(
            db=db,
            user_id=user_id,
            title=note_title,
            content=note_content,
            tags=note_tags
        )

        return InboxItemAddToNotesResponse(
            success=True,
            note_id=note.id,
            message=f"Successfully added content to notes as '{note_title}'"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding to notes: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add content to notes"
        )

@router.post("/{item_id}/chat-context", response_model=InboxItemChatContextResponse)
def prepare_chat_context(
    item_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Prepare an inbox item's content for chat context injection."""
    try:
        user_id = current_user["uid"]

        # Get the inbox item
        inbox_item = crud.get_inbox_item_by_id(db=db, item_id=item_id, user_id=user_id)
        if not inbox_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Inbox item not found"
            )

        # For now, we'll return a context ID that the chat system can use
        # In a full implementation, this would store the context in a temporary store
        context_id = f"inbox_{item_id}_{user_id}"

        return InboxItemChatContextResponse(
            success=True,
            context_id=context_id,
            message="Content prepared for chat context"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error preparing chat context: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to prepare chat context"
        )

@router.get("/categories", response_model=List[str])
def get_available_categories(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all available categories for the user."""
    user_id = current_user["uid"]

    # Try cache first
    cached_categories = smart_capture_cache.get_cached_user_categories(user_id)
    if cached_categories is not None:
        return cached_categories

    # Get from database and cache
    categories = crud.get_available_categories(db=db, user_id=user_id)
    smart_capture_cache.cache_user_categories(user_id, categories)
    return categories

@router.get("/tags", response_model=List[str])
def get_available_tags(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all available tags for the user."""
    user_id = current_user["uid"]

    # Try cache first
    cached_tags = smart_capture_cache.get_cached_user_tags(user_id)
    if cached_tags is not None:
        return cached_tags

    # Get from database and cache
    tags = crud.get_available_tags(db=db, user_id=user_id)
    smart_capture_cache.cache_user_tags(user_id, tags)
    return tags
