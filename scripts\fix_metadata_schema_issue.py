#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the metadata column schema issue.

This script will:
1. Check if both 'metadata' and 'item_metadata' columns exist
2. If both exist, drop the 'metadata' column (SQLAlchemy conflict)
3. Ensure only 'item_metadata' column remains
"""

import os
import sys
from dotenv import load_dotenv
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

def get_database_connection():
    """Get database connection from environment variables."""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError("DATABASE_URL environment variable not set")
    
    # Parse the database URL
    if database_url.startswith("postgresql://"):
        database_url = database_url.replace("postgresql://", "postgres://")
    
    return psycopg2.connect(database_url)

def fix_metadata_schema():
    """Fix the metadata column schema issue."""
    conn = None
    cursor = None
    
    try:
        print("=== Fixing Metadata Schema Issue ===")
        print("Connecting to database...")
        conn = get_database_connection()
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check current state
        print("Checking current metadata columns...")
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'inbox_items' 
            AND column_name IN ('metadata', 'item_metadata')
            ORDER BY column_name
        """)
        
        existing_columns = [row[0] for row in cursor.fetchall()]
        print(f"Found columns: {existing_columns}")
        
        if 'metadata' in existing_columns and 'item_metadata' in existing_columns:
            print("Both 'metadata' and 'item_metadata' columns exist.")
            print("Dropping 'metadata' column to fix SQLAlchemy conflict...")
            
            # Check if metadata column has any data
            cursor.execute("SELECT COUNT(*) FROM inbox_items WHERE metadata IS NOT NULL")
            metadata_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM inbox_items WHERE item_metadata IS NOT NULL")
            item_metadata_count = cursor.fetchone()[0]
            
            print(f"Data analysis:")
            print(f"  - 'metadata' column has {metadata_count} non-null rows")
            print(f"  - 'item_metadata' column has {item_metadata_count} non-null rows")
            
            if metadata_count > 0 and item_metadata_count == 0:
                print("Migrating data from 'metadata' to 'item_metadata'...")
                cursor.execute("""
                    UPDATE inbox_items 
                    SET item_metadata = metadata 
                    WHERE metadata IS NOT NULL AND item_metadata IS NULL
                """)
                print("Data migration completed.")
            
            # Drop the metadata column
            cursor.execute("ALTER TABLE inbox_items DROP COLUMN metadata")
            print("✅ Successfully dropped 'metadata' column")
            
        elif 'metadata' in existing_columns and 'item_metadata' not in existing_columns:
            print("Only 'metadata' column exists. Renaming to 'item_metadata'...")
            cursor.execute("ALTER TABLE inbox_items RENAME COLUMN metadata TO item_metadata")
            print("✅ Successfully renamed 'metadata' to 'item_metadata'")
            
        elif 'item_metadata' in existing_columns and 'metadata' not in existing_columns:
            print("✅ Only 'item_metadata' column exists. Schema is correct!")
            
        else:
            print("⚠️  No metadata columns found. This might indicate a different issue.")
        
        # Verify final state
        print("\nVerifying final schema...")
        cursor.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'inbox_items' 
            AND column_name LIKE '%metadata%'
            ORDER BY column_name
        """)
        
        final_columns = cursor.fetchall()
        print("Final metadata columns:")
        for column_name, data_type in final_columns:
            print(f"  - {column_name}: {data_type}")
        
        if len(final_columns) == 1 and final_columns[0][0] == 'item_metadata':
            print("\n✅ Schema fix completed successfully!")
            return True
        else:
            print("\n❌ Schema fix may not have worked as expected")
            return False
        
    except Exception as e:
        print(f"❌ Error during schema fix: {e}")
        if conn:
            conn.rollback()
        raise
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def main():
    """Main function."""
    print("Smart Capture Metadata Schema Fix")
    print("This script fixes the metadata column naming conflict.\n")
    
    try:
        success = fix_metadata_schema()
        if success:
            print("\n🎉 Schema fix completed successfully!")
            print("The database now has the correct 'item_metadata' column.")
            print("You can now run your application without SQLAlchemy conflicts.")
            return 0
        else:
            print("\n❌ Schema fix encountered issues!")
            return 1
    
    except Exception as e:
        print(f"\n💥 Schema fix failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
