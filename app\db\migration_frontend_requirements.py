"""
Database migration script for frontend requirements implementation.

This script creates all the necessary database schema changes to support
the frontend requirements documented in message_backend.md.

Changes include:
1. Enhanced User model with profile fields
2. UserSession model for session tracking
3. Notification model for scheduled notifications
4. Updated NotificationPreference model

Run this script to apply all database changes.
"""

import logging
from sqlalchemy import text
from sqlalchemy.orm import Session
from app.db.database import engine, get_db
from app.db.models import Base

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_migration():
    """Run the complete database migration for frontend requirements."""
    
    logger.info("Starting database migration for frontend requirements...")
    
    try:
        # Create all tables based on updated models
        logger.info("Creating/updating database tables...")
        Base.metadata.create_all(bind=engine)
        
        # Get database session
        db = next(get_db())
        
        # Add new columns to existing users table
        logger.info("Adding new columns to users table...")
        add_user_profile_columns(db)
        
        # Create new tables
        logger.info("Creating user_sessions table...")
        create_user_sessions_table(db)

        logger.info("Creating notifications table...")
        create_notifications_table(db)

        # Update notification_preferences table structure
        logger.info("Updating notification_preferences table...")
        update_notification_preferences_table(db)

        logger.info("Database migration completed successfully!")
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise
    finally:
        db.close()

def add_user_profile_columns(db: Session):
    """Add new profile columns to users table."""
    
    columns_to_add = [
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS display_name VARCHAR",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS darvis_name VARCHAR",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_picture_url VARCHAR",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_picture_public_id VARCHAR",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS member_since TIMESTAMP WITH TIME ZONE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS current_streak INTEGER DEFAULT 0",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS last_activity_date TIMESTAMP WITH TIME ZONE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS subscription_plan VARCHAR DEFAULT 'Essential'",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS subscription_expiry_date TIMESTAMP WITH TIME ZONE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS is_subscription_active BOOLEAN DEFAULT FALSE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS time_zone VARCHAR DEFAULT 'UTC'",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS language VARCHAR DEFAULT 'en'",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS theme VARCHAR DEFAULT 'system'",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS biometric_auth_enabled BOOLEAN DEFAULT FALSE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS session_timeout VARCHAR DEFAULT '30 minutes'",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS app_lock_required BOOLEAN DEFAULT FALSE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS cloud_sync_enabled BOOLEAN DEFAULT TRUE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS auto_backup_enabled BOOLEAN DEFAULT TRUE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS last_backup_date TIMESTAMP WITH TIME ZONE"
    ]
    
    for sql in columns_to_add:
        try:
            db.execute(text(sql))
            logger.info(f"Executed: {sql}")
        except Exception as e:
            logger.warning(f"Column may already exist or error occurred: {e}")
    
    db.commit()

def update_notification_preferences_table(db: Session):
    """Update notification_preferences table to match frontend requirements."""
    
    # Add new notification preference columns
    new_columns = [
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS therapy_reminders BOOLEAN DEFAULT TRUE",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS task_notifications BOOLEAN DEFAULT TRUE", 
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS daily_check_ins BOOLEAN DEFAULT FALSE",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS weekly_reports BOOLEAN DEFAULT TRUE",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS system_updates BOOLEAN DEFAULT FALSE",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS contextual_ai BOOLEAN DEFAULT TRUE",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS quiet_hours_enabled BOOLEAN DEFAULT FALSE",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS quiet_hours_start_hour INTEGER DEFAULT 22",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS quiet_hours_start_minute INTEGER DEFAULT 0",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS quiet_hours_end_hour INTEGER DEFAULT 8",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS quiet_hours_end_minute INTEGER DEFAULT 0",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS notification_frequency VARCHAR DEFAULT 'Normal'",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS sound_enabled BOOLEAN DEFAULT TRUE",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS vibration_enabled BOOLEAN DEFAULT TRUE",
        "ALTER TABLE notification_preferences ADD COLUMN IF NOT EXISTS badge_count_enabled BOOLEAN DEFAULT TRUE"
    ]
    
    for sql in new_columns:
        try:
            db.execute(text(sql))
            logger.info(f"Executed: {sql}")
        except Exception as e:
            logger.warning(f"Column may already exist or error occurred: {e}")
    
    db.commit()

def create_user_sessions_table(db: Session):
    """Create user_sessions table if it doesn't exist."""
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS user_sessions (
        id VARCHAR PRIMARY KEY,
        user_id VARCHAR NOT NULL,
        session_type VARCHAR NOT NULL,
        timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    );
    """
    
    try:
        db.execute(text(create_table_sql))
        logger.info("Created user_sessions table")
        db.commit()
    except Exception as e:
        logger.warning(f"user_sessions table may already exist: {e}")

def create_notifications_table(db: Session):
    """Create notifications table if it doesn't exist."""
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS notifications (
        id VARCHAR PRIMARY KEY,
        user_id VARCHAR NOT NULL,
        title VARCHAR NOT NULL,
        body TEXT NOT NULL,
        type VARCHAR NOT NULL,
        priority VARCHAR NOT NULL,
        scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
        navigation_route VARCHAR,
        navigation_data JSONB,
        is_delivered BOOLEAN DEFAULT FALSE,
        is_read BOOLEAN DEFAULT FALSE,
        is_interacted BOOLEAN DEFAULT FALSE,
        delivered_at TIMESTAMP WITH TIME ZONE,
        read_at TIMESTAMP WITH TIME ZONE,
        interacted_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    );
    """
    
    try:
        db.execute(text(create_table_sql))
        logger.info("Created notifications table")
        db.commit()
    except Exception as e:
        logger.warning(f"notifications table may already exist: {e}")

if __name__ == "__main__":
    run_migration()
