from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException
from contextlib import asynccontextmanager
import firebase_admin
from firebase_admin import credentials
import os
from typing import Dict, Any
from app.api.auth import get_current_user
from app.db.database import engine, init_redis, close_db_connections, enable_pgvector
from app.db.models import Base
from app.api import tasks, notes, users, chat, inbox, agent_management, commands, pronunciations, contacts, lessons, notifications, therapy, dashboard, calendar, attachments, sync, contacts_extended, social_platforms

# A "lifespan" manager to handle startup and shutdown events.
# We will add database connections here later.
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("INFO:     Lifespan startup event.")

    # Initialize Firebase Admin SDK
    try:
        # Check if Firebase app is already initialized
        if not firebase_admin._apps:
            # Try to initialize with service account credentials from environment
            cred_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
            if cred_path and os.path.exists(cred_path):
                # Initialize with service account file
                cred = credentials.Certificate(cred_path)
                firebase_admin.initialize_app(cred)
                print("INFO:     Firebase Admin SDK initialized with service account credentials.")
            else:
                # Initialize with default credentials (for Google Cloud environments)
                firebase_admin.initialize_app()
                print("INFO:     Firebase Admin SDK initialized with default credentials.")
        else:
            print("INFO:     Firebase Admin SDK already initialized.")
    except Exception as e:
        print(f"ERROR:    Failed to initialize Firebase Admin SDK: {e}")
        # Continue startup even if Firebase initialization fails for development

    # Initialize database tables and pgvector extension
    try:
        # Enable pgvector extension first
        enable_pgvector()

        # Create tables
        Base.metadata.create_all(bind=engine)
        print("INFO:     Database tables created/verified successfully.")
    except Exception as e:
        print(f"ERROR:    Failed to create database tables: {e}")

    # Initialize Redis connection
    try:
        init_redis()
        print("INFO:     Redis connection initialized.")
    except Exception as e:
        print(f"ERROR:    Failed to initialize Redis: {e}")

    # Yield control back to the application
    yield

    # Cleanup on shutdown
    print("INFO:     Lifespan shutdown event.")
    close_db_connections()

# Create the main FastAPI application instance with the lifespan manager.
app = FastAPI(
    title="Darvis Backend API",
    version="0.1.0",
    lifespan=lifespan
)

# Include API routers
app.include_router(users.router)
app.include_router(tasks.router)
app.include_router(notes.router)
app.include_router(chat.router)
app.include_router(inbox.router)
app.include_router(agent_management.router)
app.include_router(commands.router)
app.include_router(pronunciations.router)
app.include_router(contacts.router)
app.include_router(contacts_extended.router)
app.include_router(social_platforms.router)
app.include_router(lessons.router)
app.include_router(notifications.router)
app.include_router(therapy.router)
app.include_router(dashboard.router)
app.include_router(calendar.router)
app.include_router(attachments.router)
app.include_router(sync.router)

# Define a root endpoint for health checks.
@app.get("/", tags=["Health Check"])
def read_root():
    """
    Root endpoint to confirm the API is running.
    """
    return {"status": "ok", "message": "Darvis API is online."}

# Protected endpoint to get current user information
@app.get("/api/v1/users/me", tags=["Authentication"])
def get_user_profile(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Protected endpoint that returns the authenticated user's information.

    Requires a valid Firebase ID token in the Authorization header.

    Returns:
        Dict containing user information from the verified token
    """
    return {
        "uid": current_user.get("uid"),
        "email": current_user.get("email"),
        "email_verified": current_user.get("email_verified"),
        "name": current_user.get("name"),
        "picture": current_user.get("picture"),
        "firebase": {
            "sign_in_provider": current_user.get("firebase", {}).get("sign_in_provider"),
            "identities": current_user.get("firebase", {}).get("identities")
        },
        "auth_time": current_user.get("auth_time"),
        "iat": current_user.get("iat"),
        "exp": current_user.get("exp")
    }