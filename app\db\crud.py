from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, text, or_
from app.db.models import User, Task, Note, CalendarEvent, Attachment, SyncStatus, Conversation, Message, ActionLog, InboxItem, UserCommand, Pronunciation, Contact, Lesson, NotificationPreference, NotificationHistory, TherapyKnowledgeChunk, TherapySession, TherapyProgress, UserSession, Notification
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import uuid

# User CRUD operations
def create_user(db: Session, user_id: str, email: str) -> User:
    """Create a new user with Firebase UID and email"""
    db_user = User(id=user_id, email=email)
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def get_user_by_id(db: Session, user_id: str) -> Optional[User]:
    """Get user by Firebase UID"""
    return db.query(User).filter(User.id == user_id).first()

def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """Get user by email address"""
    return db.query(User).filter(User.email == email).first()

def update_user_email(db: Session, user_id: str, email: str) -> Optional[User]:
    """Update user's email address"""
    db_user = get_user_by_id(db, user_id)
    if db_user:
        db_user.email = email
        db.commit()
        db.refresh(db_user)
    return db_user

def update_user_profile(db: Session, user_id: str, profile_data: Dict[str, Any]) -> Optional[User]:
    """Update user profile with enhanced fields"""
    db_user = get_user_by_id(db, user_id)
    if db_user:
        for field, value in profile_data.items():
            if hasattr(db_user, field):
                setattr(db_user, field, value)
        db.commit()
        db.refresh(db_user)
    return db_user

def update_user_activity(db: Session, user_id: str) -> Optional[User]:
    """Update user's last activity date"""
    db_user = get_user_by_id(db, user_id)
    if db_user:
        db_user.last_activity_date = datetime.utcnow()
        db.commit()
        db.refresh(db_user)
    return db_user

# UserSession CRUD operations
def create_user_session(db: Session, user_id: str, session_type: str, timestamp: datetime) -> UserSession:
    """Create a new user session record"""
    db_session = UserSession(
        user_id=user_id,
        session_type=session_type,
        timestamp=timestamp
    )
    db.add(db_session)
    db.commit()
    db.refresh(db_session)
    return db_session

def get_user_sessions_today(db: Session, user_id: str) -> List[UserSession]:
    """Get all user sessions for today"""
    today = datetime.utcnow().date()
    return db.query(UserSession).filter(
        and_(
            UserSession.user_id == user_id,
            UserSession.timestamp >= today,
            UserSession.timestamp < today + timedelta(days=1)
        )
    ).order_by(UserSession.timestamp).all()

def get_user_session_data(db: Session, user_id: str) -> Dict[str, Any]:
    """Get user session analytics data"""
    today = datetime.utcnow().date()

    # Get today's sessions
    daily_sessions = db.query(UserSession).filter(
        and_(
            UserSession.user_id == user_id,
            UserSession.timestamp >= today,
            UserSession.timestamp < today + timedelta(days=1)
        )
    ).count()

    # Get last session
    last_session = db.query(UserSession).filter(
        UserSession.user_id == user_id
    ).order_by(desc(UserSession.timestamp)).first()

    # Get first session today
    first_session_today = db.query(UserSession).filter(
        and_(
            UserSession.user_id == user_id,
            UserSession.timestamp >= today,
            UserSession.timestamp < today + timedelta(days=1)
        )
    ).order_by(UserSession.timestamp).first()

    # Get total sessions
    total_sessions = db.query(UserSession).filter(
        UserSession.user_id == user_id
    ).count()

    return {
        "daily_sessions": daily_sessions,
        "last_session": last_session.timestamp.isoformat() if last_session else None,
        "first_session_today": first_session_today.timestamp.isoformat() if first_session_today else None,
        "total_sessions": total_sessions
    }

# Notification CRUD operations
def create_notification(
    db: Session,
    user_id: str,
    title: str,
    body: str,
    notification_type: str,
    priority: str,
    scheduled_time: datetime,
    navigation_route: Optional[str] = None,
    navigation_data: Optional[Dict[str, Any]] = None
) -> Notification:
    """Create a new notification"""
    db_notification = Notification(
        user_id=user_id,
        title=title,
        body=body,
        type=notification_type,
        priority=priority,
        scheduled_time=scheduled_time,
        navigation_route=navigation_route,
        navigation_data=navigation_data
    )
    db.add(db_notification)
    db.commit()
    db.refresh(db_notification)
    return db_notification

def get_user_notifications(
    db: Session,
    user_id: str,
    page: int = 1,
    limit: int = 20
) -> Dict[str, Any]:
    """Get paginated user notifications"""
    offset = (page - 1) * limit

    notifications = db.query(Notification).filter(
        Notification.user_id == user_id
    ).order_by(desc(Notification.created_at)).offset(offset).limit(limit).all()

    total = db.query(Notification).filter(
        Notification.user_id == user_id
    ).count()

    return {
        "notifications": notifications,
        "total": total,
        "page": page,
        "limit": limit
    }

def mark_notification_interacted(db: Session, notification_id: str) -> Optional[Notification]:
    """Mark a notification as interacted"""
    notification = db.query(Notification).filter(Notification.id == notification_id).first()
    if notification:
        notification.is_interacted = True
        notification.interacted_at = datetime.utcnow()
        db.commit()
        db.refresh(notification)
    return notification

def update_notification(
    db: Session,
    notification_id: str,
    user_id: str,
    update_data: Dict[str, Any]
) -> Optional[Notification]:
    """Update a notification's details"""
    notification = db.query(Notification).filter(
        and_(
            Notification.id == notification_id,
            Notification.user_id == user_id
        )
    ).first()

    if notification:
        # Only allow updating certain fields
        allowed_fields = ['title', 'body', 'scheduled_time', 'navigation_route', 'navigation_data', 'priority']
        for field, value in update_data.items():
            if field in allowed_fields and hasattr(notification, field):
                setattr(notification, field, value)

        db.commit()
        db.refresh(notification)

    return notification

def delete_notification(db: Session, notification_id: str, user_id: str) -> bool:
    """Delete a notification (only if not yet delivered)"""
    notification = db.query(Notification).filter(
        and_(
            Notification.id == notification_id,
            Notification.user_id == user_id,
            Notification.is_delivered == False  # Only allow deleting undelivered notifications
        )
    ).first()

    if notification:
        db.delete(notification)
        db.commit()
        return True

    return False

def get_notification_by_id(db: Session, notification_id: str, user_id: str) -> Optional[Notification]:
    """Get a specific notification by ID for a user"""
    return db.query(Notification).filter(
        and_(
            Notification.id == notification_id,
            Notification.user_id == user_id
        )
    ).first()

def mark_notification_read(db: Session, notification_id: str, user_id: str) -> Optional[Notification]:
    """Mark a notification as read"""
    notification = db.query(Notification).filter(
        and_(
            Notification.id == notification_id,
            Notification.user_id == user_id
        )
    ).first()

    if notification:
        notification.is_read = True
        notification.read_at = datetime.utcnow()
        db.commit()
        db.refresh(notification)

    return notification

def get_unread_notification_count(db: Session, user_id: str) -> int:
    """Get count of unread notifications for a user"""
    return db.query(Notification).filter(
        and_(
            Notification.user_id == user_id,
            Notification.is_read == False,
            Notification.is_delivered == True
        )
    ).count()

def mark_all_notifications_read(db: Session, user_id: str) -> int:
    """Mark all notifications as read for a user"""
    updated_count = db.query(Notification).filter(
        and_(
            Notification.user_id == user_id,
            Notification.is_read == False
        )
    ).update({
        "is_read": True,
        "read_at": datetime.utcnow()
    })

    db.commit()
    return updated_count

# Notification Queue Management CRUD operations
def record_delivery_attempt(
    db: Session,
    notification_id: str,
    success: bool,
    failure_reason: Optional[str] = None
) -> Optional[Notification]:
    """Record a delivery attempt for a notification"""
    notification = db.query(Notification).filter(Notification.id == notification_id).first()

    if notification:
        notification.delivery_attempts += 1
        notification.last_attempt_at = datetime.utcnow()

        if success:
            notification.is_delivered = True
            notification.delivered_at = datetime.utcnow()
            notification.failure_reason = None
        else:
            notification.failure_reason = failure_reason

            # Mark as failed after 3 attempts
            if notification.delivery_attempts >= 3:
                notification.is_failed = True
            else:
                # Calculate retry delay (exponential backoff)
                retry_delay_minutes = 2 ** notification.delivery_attempts  # 2, 4, 8 minutes
                notification.retry_after = datetime.utcnow() + timedelta(minutes=retry_delay_minutes)

        db.commit()
        db.refresh(notification)

    return notification

def get_notifications_ready_for_delivery(db: Session, limit: int = 50) -> List[Notification]:
    """Get notifications that are ready for delivery"""
    current_time = datetime.utcnow()

    return db.query(Notification).filter(
        and_(
            Notification.scheduled_time <= current_time,
            Notification.is_delivered == False,
            Notification.is_failed == False,
            or_(
                Notification.retry_after.is_(None),
                Notification.retry_after <= current_time
            )
        )
    ).order_by(Notification.priority.desc(), Notification.scheduled_time).limit(limit).all()

def get_failed_notifications(db: Session, user_id: Optional[str] = None, limit: int = 100) -> List[Notification]:
    """Get failed notifications for analysis"""
    query = db.query(Notification).filter(Notification.is_failed == True)

    if user_id:
        query = query.filter(Notification.user_id == user_id)

    return query.order_by(desc(Notification.last_attempt_at)).limit(limit).all()

def reset_failed_notification(db: Session, notification_id: str) -> Optional[Notification]:
    """Reset a failed notification for retry"""
    notification = db.query(Notification).filter(Notification.id == notification_id).first()

    if notification and notification.is_failed:
        notification.is_failed = False
        notification.delivery_attempts = 0
        notification.failure_reason = None
        notification.retry_after = None
        notification.last_attempt_at = None

        db.commit()
        db.refresh(notification)

    return notification

def get_notification_delivery_stats(db: Session, user_id: Optional[str] = None, days: int = 7) -> Dict[str, Any]:
    """Get notification delivery statistics"""
    start_date = datetime.utcnow() - timedelta(days=days)

    query = db.query(Notification).filter(Notification.created_at >= start_date)
    if user_id:
        query = query.filter(Notification.user_id == user_id)

    total_notifications = query.count()
    delivered_notifications = query.filter(Notification.is_delivered == True).count()
    failed_notifications = query.filter(Notification.is_failed == True).count()
    pending_notifications = query.filter(
        and_(
            Notification.is_delivered == False,
            Notification.is_failed == False
        )
    ).count()

    # Calculate average delivery time for delivered notifications
    delivered_query = query.filter(
        and_(
            Notification.is_delivered == True,
            Notification.delivered_at.isnot(None)
        )
    )

    avg_delivery_delay = None
    if delivered_query.count() > 0:
        delivery_delays = []
        for notification in delivered_query.all():
            if notification.delivered_at and notification.scheduled_time:
                delay = (notification.delivered_at - notification.scheduled_time).total_seconds()
                delivery_delays.append(max(0, delay))  # Only positive delays

        if delivery_delays:
            avg_delivery_delay = sum(delivery_delays) / len(delivery_delays)

    return {
        "total_notifications": total_notifications,
        "delivered_notifications": delivered_notifications,
        "failed_notifications": failed_notifications,
        "pending_notifications": pending_notifications,
        "delivery_rate": delivered_notifications / total_notifications if total_notifications > 0 else 0,
        "failure_rate": failed_notifications / total_notifications if total_notifications > 0 else 0,
        "average_delivery_delay_seconds": avg_delivery_delay,
        "period_days": days
    }

# Task CRUD operations
def create_user_task(
    db: Session,
    user_id: str,
    content: str,
    due_date: Optional[datetime] = None,
    priority: int = 0,
    tags: Optional[List[str]] = None,
    is_recurring: bool = False,
    parent_task_id: Optional[str] = None,
    estimated_duration_minutes: Optional[int] = None,
    depends_on_task_ids: Optional[List[str]] = None,
    is_template: bool = False,
    template_data: Optional[Dict[str, Any]] = None,
    assigned_to_user_id: Optional[str] = None
) -> Task:
    """Create a new task for a user with enhanced fields"""
    task_id = str(uuid.uuid4())
    db_task = Task(
        id=task_id,
        content=content,
        owner_id=user_id,
        due_date=due_date,
        priority=priority,
        tags=tags or [],
        is_recurring=is_recurring,
        parent_task_id=parent_task_id,
        estimated_duration_minutes=estimated_duration_minutes,
        depends_on_task_ids=depends_on_task_ids or [],
        is_template=is_template,
        template_data=template_data,
        assigned_to_user_id=assigned_to_user_id,
        assigned_by_user_id=user_id if assigned_to_user_id else None
    )
    db.add(db_task)
    db.commit()
    db.refresh(db_task)
    return db_task

def get_user_tasks(
    db: Session,
    user_id: str,
    skip: int = 0,
    limit: int = 100,
    priority: Optional[int] = None,
    tags: Optional[List[str]] = None,
    is_completed: Optional[bool] = None,
    is_recurring: Optional[bool] = None,
    due_before: Optional[datetime] = None,
    due_after: Optional[datetime] = None
) -> List[Task]:
    """Get tasks for a user with filtering and pagination"""
    query = db.query(Task).filter(Task.owner_id == user_id)

    # Apply filters
    if priority is not None:
        query = query.filter(Task.priority == priority)

    if is_completed is not None:
        query = query.filter(Task.is_completed == is_completed)

    if is_recurring is not None:
        query = query.filter(Task.is_recurring == is_recurring)

    if due_before is not None:
        query = query.filter(Task.due_date <= due_before)

    if due_after is not None:
        query = query.filter(Task.due_date >= due_after)

    if tags:
        # Filter tasks that contain any of the specified tags
        tag_conditions = [Task.tags.contains([tag]) for tag in tags]
        query = query.filter(or_(*tag_conditions))

    return query.order_by(desc(Task.priority), Task.due_date.asc().nullslast()).offset(skip).limit(limit).all()

def get_task_by_id(db: Session, task_id: str, user_id: str) -> Optional[Task]:
    """Get a specific task by ID, ensuring it belongs to the user"""
    return db.query(Task).filter(
        and_(Task.id == task_id, Task.owner_id == user_id)
    ).first()

def update_task(db: Session, task_id: str, user_id: str, task_data: Dict[str, Any]) -> Optional[Task]:
    """Update a task's content and/or completion status"""
    db_task = get_task_by_id(db, task_id, user_id)
    if db_task:
        for field, value in task_data.items():
            if hasattr(db_task, field):
                setattr(db_task, field, value)
        db.commit()
        db.refresh(db_task)
    return db_task

def delete_task(db: Session, task_id: str, user_id: str) -> bool:
    """Delete a task, ensuring it belongs to the user"""
    db_task = get_task_by_id(db, task_id, user_id)
    if db_task:
        db.delete(db_task)
        db.commit()
        return True
    return False

# Note CRUD operations
def create_user_note(
    db: Session,
    user_id: str,
    title: str,
    content: str,
    tags: Optional[List[str]] = None,
    content_type: str = "plain",
    content_data: Optional[Dict[str, Any]] = None,
    category: Optional[str] = None,
    is_template: bool = False,
    is_shared: bool = False,
    shared_with: Optional[List[str]] = None
) -> Note:
    """Create a new note for a user with enhanced features"""
    note_id = str(uuid.uuid4())
    db_note = Note(
        id=note_id,
        title=title,
        content=content,
        content_type=content_type,
        content_data=content_data,
        category=category,
        tags=tags or [],
        is_template=is_template,
        is_shared=is_shared,
        shared_with=shared_with or [],
        owner_id=user_id
    )
    db.add(db_note)
    db.commit()
    db.refresh(db_note)
    return db_note

def get_user_notes(
    db: Session,
    user_id: str,
    skip: int = 0,
    limit: int = 100,
    tags: Optional[List[str]] = None,
    search_query: Optional[str] = None
) -> List[Note]:
    """Get notes for a user with filtering and pagination"""
    query = db.query(Note).filter(Note.owner_id == user_id)

    # Apply filters
    if tags:
        # Filter notes that contain any of the specified tags
        tag_conditions = [Note.tags.contains([tag]) for tag in tags]
        query = query.filter(or_(*tag_conditions))

    if search_query:
        # Search in title and content
        search_pattern = f"%{search_query}%"
        query = query.filter(
            or_(
                Note.title.ilike(search_pattern),
                Note.content.ilike(search_pattern)
            )
        )

    return query.order_by(desc(Note.updated_at)).offset(skip).limit(limit).all()

def get_note_by_id(db: Session, note_id: str, user_id: str) -> Optional[Note]:
    """Get a specific note by ID, ensuring it belongs to the user"""
    return db.query(Note).filter(
        and_(Note.id == note_id, Note.owner_id == user_id)
    ).first()

def update_note(db: Session, note_id: str, user_id: str, note_data: Dict[str, Any]) -> Optional[Note]:
    """Update a note's title and/or content"""
    db_note = get_note_by_id(db, note_id, user_id)
    if db_note:
        for field, value in note_data.items():
            if hasattr(db_note, field):
                setattr(db_note, field, value)
        db.commit()
        db.refresh(db_note)
    return db_note

def delete_note(db: Session, note_id: str, user_id: str) -> bool:
    """Delete a note, ensuring it belongs to the user"""
    db_note = get_note_by_id(db, note_id, user_id)
    if db_note:
        db.delete(db_note)
        db.commit()
        return True
    return False

def count_user_tasks(db: Session, user_id: str, is_completed: Optional[bool] = None) -> int:
    """Count total tasks for a user with optional completion filter"""
    query = db.query(Task).filter(Task.owner_id == user_id)
    if is_completed is not None:
        query = query.filter(Task.is_completed == is_completed)
    return query.count()

def count_user_notes(db: Session, user_id: str) -> int:
    """Count total notes for a user"""
    return db.query(Note).filter(Note.owner_id == user_id).count()

def get_tasks_due_today(db: Session, user_id: str, include_overdue: bool = True) -> List[Task]:
    """Get tasks due today and optionally overdue tasks"""
    from datetime import datetime, timedelta

    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = today_start + timedelta(days=1)

    query = db.query(Task).filter(Task.owner_id == user_id)

    if include_overdue:
        # Include tasks due today or overdue (due before today and not completed)
        query = query.filter(
            or_(
                and_(Task.due_date >= today_start, Task.due_date < today_end),
                and_(Task.due_date < today_start, Task.is_completed == False)
            )
        )
    else:
        # Only tasks due today
        query = query.filter(
            and_(Task.due_date >= today_start, Task.due_date < today_end)
        )

    return query.order_by(desc(Task.priority), Task.due_date.asc().nullslast()).all()

def get_tasks_completed_today(db: Session, user_id: str) -> List[Task]:
    """Get tasks completed today"""
    from datetime import datetime, timedelta

    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = today_start + timedelta(days=1)

    return db.query(Task).filter(
        and_(
            Task.owner_id == user_id,
            Task.is_completed == True,
            Task.updated_at >= today_start,
            Task.updated_at < today_end
        )
    ).all()

def get_notes_created_today(db: Session, user_id: str) -> List[Note]:
    """Get notes created today"""
    from datetime import datetime, timedelta

    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = today_start + timedelta(days=1)

    return db.query(Note).filter(
        and_(
            Note.owner_id == user_id,
            Note.created_at >= today_start,
            Note.created_at < today_end
        )
    ).order_by(desc(Note.created_at)).all()

# Conversation CRUD operations
def create_conversation(db: Session, user_id: str, summary: Optional[str] = None) -> Conversation:
    """Create a new conversation for a user"""
    conversation_id = str(uuid.uuid4())
    db_conversation = Conversation(id=conversation_id, owner_id=user_id, summary=summary)
    db.add(db_conversation)
    db.commit()
    db.refresh(db_conversation)
    return db_conversation

def get_conversation_by_id(db: Session, conversation_id: str, user_id: str) -> Optional[Conversation]:
    """Get a specific conversation by ID, ensuring it belongs to the user"""
    return db.query(Conversation).filter(
        and_(Conversation.id == conversation_id, Conversation.owner_id == user_id)
    ).first()

def get_recent_conversations_for_user(db: Session, user_id: str, limit: int = 10) -> List[Conversation]:
    """Get recent conversations for a user, ordered by most recent activity"""
    return db.query(Conversation).filter(
        Conversation.owner_id == user_id
    ).order_by(desc(Conversation.updated_at)).limit(limit).all()

def update_conversation_summary(db: Session, conversation_id: str, user_id: str, summary: str) -> Optional[Conversation]:
    """Update conversation summary"""
    db_conversation = get_conversation_by_id(db, conversation_id, user_id)
    if db_conversation:
        db_conversation.summary = summary
        db.commit()
        db.refresh(db_conversation)
    return db_conversation

def delete_conversation(db: Session, conversation_id: str, user_id: str) -> bool:
    """Delete a conversation and all its messages, ensuring it belongs to the user"""
    db_conversation = get_conversation_by_id(db, conversation_id, user_id)
    if db_conversation:
        db.delete(db_conversation)
        db.commit()
        return True
    return False

# Message CRUD operations
def save_message(db: Session, conversation_id: str, role: str, content: str, vector: List[float]) -> Message:
    """Save a message with its vector embedding"""
    message_id = str(uuid.uuid4())
    db_message = Message(
        id=message_id,
        conversation_id=conversation_id,
        role=role,
        content=content,
        embedding=vector
    )
    db.add(db_message)
    db.commit()
    db.refresh(db_message)
    return db_message

def get_conversation_history(db: Session, conversation_id: str, limit: int = 50) -> List[Message]:
    """Get conversation history ordered by creation time"""
    return db.query(Message).filter(
        Message.conversation_id == conversation_id
    ).order_by(Message.created_at).limit(limit).all()

def get_message_by_id(db: Session, message_id: str) -> Optional[Message]:
    """Get a specific message by ID"""
    return db.query(Message).filter(Message.id == message_id).first()

def find_similar_messages(db: Session, query_vector: List[float], user_id: str, limit: int = 5) -> List[Message]:
    """Find similar messages using vector similarity search"""
    # Join with conversations to filter by user
    return db.query(Message).join(Conversation).filter(
        Conversation.owner_id == user_id
    ).order_by(
        Message.embedding.l2_distance(query_vector)
    ).limit(limit).all()

def delete_message(db: Session, message_id: str) -> bool:
    """Delete a specific message"""
    db_message = get_message_by_id(db, message_id)
    if db_message:
        db.delete(db_message)
        db.commit()
        return True
    return False

# ActionLog CRUD operations
def log_agent_action(
    db: Session,
    conversation_id: str,
    tool_name: str,
    input_params: Dict[str, Any],
    output_summary: str
) -> ActionLog:
    """Log an agent action/tool usage"""
    action_id = str(uuid.uuid4())
    db_action = ActionLog(
        id=action_id,
        conversation_id=conversation_id,
        tool_name=tool_name,
        input_params=input_params,
        output_summary=output_summary
    )
    db.add(db_action)
    db.commit()
    db.refresh(db_action)
    return db_action

def get_conversation_actions(db: Session, conversation_id: str, limit: int = 20) -> List[ActionLog]:
    """Get action logs for a conversation"""
    return db.query(ActionLog).filter(
        ActionLog.conversation_id == conversation_id
    ).order_by(desc(ActionLog.created_at)).limit(limit).all()

def get_action_by_id(db: Session, action_id: str) -> Optional[ActionLog]:
    """Get a specific action log by ID"""
    return db.query(ActionLog).filter(ActionLog.id == action_id).first()

def get_user_actions(db: Session, user_id: str, limit: int = 50) -> List[ActionLog]:
    """Get recent actions for a user across all conversations"""
    return db.query(ActionLog).join(Conversation).filter(
        Conversation.owner_id == user_id
    ).order_by(desc(ActionLog.created_at)).limit(limit).all()

def delete_action_log(db: Session, action_id: str) -> bool:
    """Delete a specific action log"""
    db_action = get_action_by_id(db, action_id)
    if db_action:
        db.delete(db_action)
        db.commit()
        return True
    return False

# InboxItem CRUD operations
def create_inbox_item(
    db: Session,
    user_id: str,
    original_url: str,
    tags: Optional[List[str]] = None,
    category: Optional[str] = None
) -> InboxItem:
    """Create a new inbox item for a user with optional Smart Capture fields"""
    item_id = str(uuid.uuid4())
    db_item = InboxItem(
        id=item_id,
        original_url=original_url,
        owner_id=user_id,
        status="pending",
        tags=tags or [],
        category=category
    )
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item

def get_inbox_item_by_id(db: Session, item_id: str, user_id: str) -> Optional[InboxItem]:
    """Get a specific inbox item by ID, ensuring it belongs to the user"""
    return db.query(InboxItem).filter(
        and_(InboxItem.id == item_id, InboxItem.owner_id == user_id)
    ).first()

def get_user_inbox_items(
    db: Session,
    user_id: str,
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None
) -> List[InboxItem]:
    """Get inbox items for a user with filtering and pagination"""
    query = db.query(InboxItem).filter(InboxItem.owner_id == user_id)

    if status:
        query = query.filter(InboxItem.status == status)

    return query.order_by(desc(InboxItem.created_at)).offset(skip).limit(limit).all()

def update_inbox_item_status(db: Session, item_id: str, status: str) -> Optional[InboxItem]:
    """Update an inbox item's status"""
    db_item = db.query(InboxItem).filter(InboxItem.id == item_id).first()
    if db_item:
        db_item.status = status
        db.commit()
        db.refresh(db_item)
    return db_item

def update_inbox_item_content(
    db: Session,
    item_id: str,
    clean_content: Optional[str] = None,
    summary: Optional[str] = None
) -> Optional[InboxItem]:
    """Update an inbox item's content and summary"""
    db_item = db.query(InboxItem).filter(InboxItem.id == item_id).first()
    if db_item:
        if clean_content is not None:
            db_item.clean_content = clean_content
        if summary is not None:
            db_item.summary = summary
        db.commit()
        db.refresh(db_item)
    return db_item

def delete_inbox_item(db: Session, item_id: str, user_id: str) -> bool:
    """Delete an inbox item, ensuring it belongs to the user"""
    db_item = get_inbox_item_by_id(db, item_id, user_id)
    if db_item:
        db.delete(db_item)
        db.commit()
        return True
    return False

# Enhanced Smart Capture CRUD operations
def update_inbox_item_enhanced(
    db: Session,
    item_id: str,
    user_id: str,
    title: Optional[str] = None,
    author: Optional[str] = None,
    publish_date: Optional[datetime] = None,
    word_count: Optional[int] = None,
    reading_time: Optional[int] = None,
    thumbnail_url: Optional[str] = None,
    tags: Optional[List[str]] = None,
    category: Optional[str] = None,
    content_type: Optional[str] = None,
    item_metadata: Optional[Dict[str, Any]] = None,
    processing_error: Optional[str] = None,
    retry_count: Optional[int] = None
) -> Optional[InboxItem]:
    """Update an inbox item with enhanced Smart Capture fields"""
    db_item = get_inbox_item_by_id(db, item_id, user_id)
    if db_item:
        if title is not None:
            db_item.title = title
        if author is not None:
            db_item.author = author
        if publish_date is not None:
            db_item.publish_date = publish_date
        if word_count is not None:
            db_item.word_count = word_count
        if reading_time is not None:
            db_item.reading_time = reading_time
        if thumbnail_url is not None:
            db_item.thumbnail_url = thumbnail_url
        if tags is not None:
            db_item.tags = tags
        if category is not None:
            db_item.category = category
        if content_type is not None:
            db_item.content_type = content_type
        if item_metadata is not None:
            db_item.item_metadata = item_metadata
        if processing_error is not None:
            db_item.processing_error = processing_error
        if retry_count is not None:
            db_item.retry_count = retry_count

        db.commit()
        db.refresh(db_item)
    return db_item

def search_inbox_items(
    db: Session,
    user_id: str,
    query: Optional[str] = None,
    tags: Optional[List[str]] = None,
    category: Optional[str] = None,
    content_type: Optional[str] = None,
    status: Optional[str] = None,
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None,
    sort_by: str = "created_at",
    sort_order: str = "desc",
    skip: int = 0,
    limit: int = 20
) -> List[InboxItem]:
    """Search inbox items with advanced filtering"""
    query_obj = db.query(InboxItem).filter(InboxItem.owner_id == user_id)

    # Text search across title and content
    if query:
        search_filter = or_(
            InboxItem.title.ilike(f"%{query}%"),
            InboxItem.clean_content.ilike(f"%{query}%"),
            InboxItem.summary.ilike(f"%{query}%"),
            InboxItem.author.ilike(f"%{query}%")
        )
        query_obj = query_obj.filter(search_filter)

    # Tag filtering
    if tags:
        for tag in tags:
            query_obj = query_obj.filter(InboxItem.tags.any(tag))

    # Category filtering
    if category:
        query_obj = query_obj.filter(InboxItem.category == category)

    # Content type filtering
    if content_type:
        query_obj = query_obj.filter(InboxItem.content_type == content_type)

    # Status filtering
    if status:
        query_obj = query_obj.filter(InboxItem.status == status)

    # Date range filtering
    if date_from:
        query_obj = query_obj.filter(InboxItem.created_at >= date_from)
    if date_to:
        query_obj = query_obj.filter(InboxItem.created_at <= date_to)

    # Sorting
    sort_column = getattr(InboxItem, sort_by, InboxItem.created_at)
    if sort_order.lower() == "desc":
        query_obj = query_obj.order_by(desc(sort_column))
    else:
        query_obj = query_obj.order_by(sort_column)

    return query_obj.offset(skip).limit(limit).all()

def get_inbox_item_count(
    db: Session,
    user_id: str,
    query: Optional[str] = None,
    tags: Optional[List[str]] = None,
    category: Optional[str] = None,
    content_type: Optional[str] = None,
    status: Optional[str] = None,
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None
) -> int:
    """Get count of inbox items matching search criteria"""
    query_obj = db.query(InboxItem).filter(InboxItem.owner_id == user_id)

    # Apply same filters as search_inbox_items
    if query:
        search_filter = or_(
            InboxItem.title.ilike(f"%{query}%"),
            InboxItem.clean_content.ilike(f"%{query}%"),
            InboxItem.summary.ilike(f"%{query}%"),
            InboxItem.author.ilike(f"%{query}%")
        )
        query_obj = query_obj.filter(search_filter)

    if tags:
        for tag in tags:
            query_obj = query_obj.filter(InboxItem.tags.any(tag))

    if category:
        query_obj = query_obj.filter(InboxItem.category == category)

    if content_type:
        query_obj = query_obj.filter(InboxItem.content_type == content_type)

    if status:
        query_obj = query_obj.filter(InboxItem.status == status)

    if date_from:
        query_obj = query_obj.filter(InboxItem.created_at >= date_from)
    if date_to:
        query_obj = query_obj.filter(InboxItem.created_at <= date_to)

    return query_obj.count()

def get_available_tags(db: Session, user_id: str) -> List[str]:
    """Get all unique tags used by a user"""
    result = db.query(InboxItem.tags).filter(
        and_(InboxItem.owner_id == user_id, InboxItem.tags.isnot(None))
    ).all()

    all_tags = set()
    for row in result:
        if row.tags:
            all_tags.update(row.tags)

    return sorted(list(all_tags))

def get_available_categories(db: Session, user_id: str) -> List[str]:
    """Get all unique categories used by a user"""
    result = db.query(InboxItem.category).filter(
        and_(InboxItem.owner_id == user_id, InboxItem.category.isnot(None))
    ).distinct().all()

    return sorted([row.category for row in result if row.category])

def bulk_delete_inbox_items(db: Session, user_id: str, item_ids: List[str]) -> int:
    """Bulk delete inbox items for a user"""
    deleted_count = db.query(InboxItem).filter(
        and_(
            InboxItem.owner_id == user_id,
            InboxItem.id.in_(item_ids)
        )
    ).delete(synchronize_session=False)

    db.commit()
    return deleted_count

def bulk_update_category(db: Session, user_id: str, item_ids: List[str], category: str) -> int:
    """Bulk update category for inbox items"""
    updated_count = db.query(InboxItem).filter(
        and_(
            InboxItem.owner_id == user_id,
            InboxItem.id.in_(item_ids)
        )
    ).update({"category": category}, synchronize_session=False)

    db.commit()
    return updated_count

def bulk_add_tags(db: Session, user_id: str, item_ids: List[str], tags: List[str]) -> int:
    """Bulk add tags to inbox items"""
    items = db.query(InboxItem).filter(
        and_(
            InboxItem.owner_id == user_id,
            InboxItem.id.in_(item_ids)
        )
    ).all()

    updated_count = 0
    for item in items:
        current_tags = set(item.tags or [])
        new_tags = current_tags.union(set(tags))
        item.tags = list(new_tags)
        updated_count += 1

    db.commit()
    return updated_count

def bulk_remove_tags(db: Session, user_id: str, item_ids: List[str], tags: List[str]) -> int:
    """Bulk remove tags from inbox items"""
    items = db.query(InboxItem).filter(
        and_(
            InboxItem.owner_id == user_id,
            InboxItem.id.in_(item_ids)
        )
    ).all()

    updated_count = 0
    for item in items:
        current_tags = set(item.tags or [])
        new_tags = current_tags - set(tags)
        item.tags = list(new_tags)
        updated_count += 1

    db.commit()
    return updated_count

# UserCommand CRUD operations
def create_user_command(db: Session, user_id: str, trigger_phrase: str, action_to_perform: str) -> UserCommand:
    """Create a new user command"""
    command_id = str(uuid.uuid4())
    db_command = UserCommand(
        id=command_id,
        trigger_phrase=trigger_phrase.strip().lower(),  # Normalize for consistent matching
        action_to_perform=action_to_perform.strip(),
        owner_id=user_id
    )
    db.add(db_command)
    db.commit()
    db.refresh(db_command)
    return db_command

def get_user_commands(db: Session, user_id: str, skip: int = 0, limit: int = 100) -> List[UserCommand]:
    """Get all commands for a user with pagination"""
    return db.query(UserCommand).filter(
        UserCommand.owner_id == user_id
    ).offset(skip).limit(limit).all()

def get_user_command_by_id(db: Session, command_id: str, user_id: str) -> Optional[UserCommand]:
    """Get a specific command by ID, ensuring it belongs to the user"""
    return db.query(UserCommand).filter(
        and_(UserCommand.id == command_id, UserCommand.owner_id == user_id)
    ).first()

def get_user_command_by_trigger(db: Session, user_id: str, trigger_phrase: str) -> Optional[UserCommand]:
    """Get a command by trigger phrase for a specific user"""
    return db.query(UserCommand).filter(
        and_(
            UserCommand.owner_id == user_id,
            UserCommand.trigger_phrase == trigger_phrase.strip().lower()
        )
    ).first()

def update_user_command(
    db: Session,
    command_id: str,
    user_id: str,
    trigger_phrase: Optional[str] = None,
    action_to_perform: Optional[str] = None
) -> Optional[UserCommand]:
    """Update a user command"""
    db_command = get_user_command_by_id(db, command_id, user_id)
    if db_command:
        if trigger_phrase is not None:
            db_command.trigger_phrase = trigger_phrase.strip().lower()
        if action_to_perform is not None:
            db_command.action_to_perform = action_to_perform.strip()
        db.commit()
        db.refresh(db_command)
    return db_command

def delete_user_command(db: Session, command_id: str, user_id: str) -> bool:
    """Delete a user command, ensuring it belongs to the user"""
    db_command = get_user_command_by_id(db, command_id, user_id)
    if db_command:
        db.delete(db_command)
        db.commit()
        return True
    return False

def count_user_commands(db: Session, user_id: str) -> int:
    """Count total commands for a user"""
    return db.query(UserCommand).filter(UserCommand.owner_id == user_id).count()

# Pronunciation CRUD operations
def create_pronunciation(db: Session, user_id: str, word_to_replace: str, phonetic_pronunciation: str) -> Pronunciation:
    """Create a new pronunciation customization"""
    pronunciation_id = str(uuid.uuid4())
    db_pronunciation = Pronunciation(
        id=pronunciation_id,
        word_to_replace=word_to_replace.strip().lower(),  # Normalize for consistent matching
        phonetic_pronunciation=phonetic_pronunciation.strip(),
        owner_id=user_id
    )
    db.add(db_pronunciation)
    db.commit()
    db.refresh(db_pronunciation)
    return db_pronunciation

def get_user_pronunciations(db: Session, user_id: str, skip: int = 0, limit: int = 100) -> List[Pronunciation]:
    """Get all pronunciations for a user with pagination"""
    return db.query(Pronunciation).filter(
        Pronunciation.owner_id == user_id
    ).offset(skip).limit(limit).all()

def get_pronunciation_by_id(db: Session, pronunciation_id: str, user_id: str) -> Optional[Pronunciation]:
    """Get a specific pronunciation by ID, ensuring it belongs to the user"""
    return db.query(Pronunciation).filter(
        and_(Pronunciation.id == pronunciation_id, Pronunciation.owner_id == user_id)
    ).first()

def get_pronunciation_by_word(db: Session, user_id: str, word: str) -> Optional[Pronunciation]:
    """Get a pronunciation by word for a specific user"""
    return db.query(Pronunciation).filter(
        and_(
            Pronunciation.owner_id == user_id,
            Pronunciation.word_to_replace == word.strip().lower()
        )
    ).first()

def update_pronunciation(
    db: Session,
    pronunciation_id: str,
    user_id: str,
    word_to_replace: Optional[str] = None,
    phonetic_pronunciation: Optional[str] = None
) -> Optional[Pronunciation]:
    """Update a pronunciation"""
    db_pronunciation = get_pronunciation_by_id(db, pronunciation_id, user_id)
    if db_pronunciation:
        if word_to_replace is not None:
            db_pronunciation.word_to_replace = word_to_replace.strip().lower()
        if phonetic_pronunciation is not None:
            db_pronunciation.phonetic_pronunciation = phonetic_pronunciation.strip()
        db.commit()
        db.refresh(db_pronunciation)
    return db_pronunciation

def delete_pronunciation(db: Session, pronunciation_id: str, user_id: str) -> bool:
    """Delete a pronunciation, ensuring it belongs to the user"""
    db_pronunciation = get_pronunciation_by_id(db, pronunciation_id, user_id)
    if db_pronunciation:
        db.delete(db_pronunciation)
        db.commit()
        return True
    return False

def count_user_pronunciations(db: Session, user_id: str) -> int:
    """Count total pronunciations for a user"""
    return db.query(Pronunciation).filter(Pronunciation.owner_id == user_id).count()

def get_all_user_pronunciations_dict(db: Session, user_id: str) -> Dict[str, str]:
    """Get all pronunciations for a user as a dictionary for fast TTS lookup"""
    pronunciations = db.query(Pronunciation).filter(Pronunciation.owner_id == user_id).all()
    return {p.word_to_replace: p.phonetic_pronunciation for p in pronunciations}

# Contact CRUD operations
def create_contact(
    db: Session,
    user_id: str,
    name: str,
    phone: Optional[str] = None,
    email: Optional[str] = None,
    location: Optional[str] = None,
    met_at: Optional[str] = None,
    social_media: Optional[Dict[str, str]] = None,
    memory_prompt: Optional[str] = None,
    image_path: Optional[str] = None,
    device_sync_status: str = 'disabled'
) -> Contact:
    """Create a new contact for a user"""
    contact_id = str(uuid.uuid4())
    db_contact = Contact(
        id=contact_id,
        name=name.strip(),
        phone=phone.strip() if phone else None,
        email=email.strip().lower() if email else None,
        location=location.strip() if location else None,
        met_at=met_at.strip() if met_at else None,
        social_media=social_media or {},
        memory_prompt=memory_prompt.strip() if memory_prompt else None,
        image_path=image_path,
        device_sync_status=device_sync_status,
        owner_id=user_id
    )
    db.add(db_contact)
    db.commit()
    db.refresh(db_contact)
    return db_contact

def get_user_contacts(
    db: Session,
    user_id: str,
    skip: int = 0,
    limit: int = 100,
    search_query: Optional[str] = None,
    include_deleted: bool = False
) -> List[Contact]:
    """Get contacts for a user with optional search and pagination"""
    query = db.query(Contact).filter(Contact.owner_id == user_id)

    # Exclude soft-deleted contacts by default
    if not include_deleted:
        query = query.filter(Contact.deleted_at.is_(None))

    if search_query:
        # Search in name, phone, email, location, met_at, and memory_prompt
        search_pattern = f"%{search_query}%"
        query = query.filter(
            or_(
                Contact.name.ilike(search_pattern),
                Contact.phone.ilike(search_pattern),
                Contact.email.ilike(search_pattern),
                Contact.location.ilike(search_pattern),
                Contact.met_at.ilike(search_pattern),
                Contact.memory_prompt.ilike(search_pattern)
            )
        )

    return query.order_by(Contact.name.asc()).offset(skip).limit(limit).all()

def get_contact_by_id(db: Session, contact_id: str, user_id: str, include_deleted: bool = False) -> Optional[Contact]:
    """Get a specific contact by ID, ensuring it belongs to the user"""
    query = db.query(Contact).filter(
        and_(Contact.id == contact_id, Contact.owner_id == user_id)
    )

    # Exclude soft-deleted contacts by default
    if not include_deleted:
        query = query.filter(Contact.deleted_at.is_(None))

    return query.first()

def update_contact(db: Session, contact_id: str, user_id: str, contact_data: Dict[str, Any]) -> Optional[Contact]:
    """Update a contact's information"""
    db_contact = get_contact_by_id(db, contact_id, user_id)
    if db_contact:
        for field, value in contact_data.items():
            if hasattr(db_contact, field):
                if field in ['name', 'phone', 'location', 'met_at', 'memory_prompt'] and value:
                    setattr(db_contact, field, value.strip())
                elif field == 'email' and value:
                    setattr(db_contact, field, value.strip().lower())
                else:
                    setattr(db_contact, field, value)
        db.commit()
        db.refresh(db_contact)
    return db_contact

def delete_contact(db: Session, contact_id: str, user_id: str, soft_delete: bool = True) -> bool:
    """Delete a contact, ensuring it belongs to the user"""
    db_contact = get_contact_by_id(db, contact_id, user_id)
    if db_contact:
        if soft_delete:
            # Soft delete - set deleted_at timestamp
            db_contact.deleted_at = func.now()
            db.commit()
        else:
            # Hard delete - permanently remove from database
            db.delete(db_contact)
            db.commit()
        return True
    return False

def count_user_contacts(db: Session, user_id: str, include_deleted: bool = False) -> int:
    """Count total contacts for a user"""
    query = db.query(Contact).filter(Contact.owner_id == user_id)
    if not include_deleted:
        query = query.filter(Contact.deleted_at.is_(None))
    return query.count()

# Additional Contact CRUD operations for Phase 3

def search_contacts(
    db: Session,
    user_id: str,
    query: str,
    fields: Optional[List[str]] = None,
    platform: Optional[str] = None,
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 20
) -> List[Contact]:
    """Advanced contact search with filters"""
    db_query = db.query(Contact).filter(
        and_(Contact.owner_id == user_id, Contact.deleted_at.is_(None))
    )

    # Text search
    search_pattern = f"%{query}%"
    if fields:
        # Search in specific fields
        search_conditions = []
        for field in fields:
            if hasattr(Contact, field):
                search_conditions.append(getattr(Contact, field).ilike(search_pattern))
        if search_conditions:
            db_query = db_query.filter(or_(*search_conditions))
    else:
        # Search in all text fields
        db_query = db_query.filter(
            or_(
                Contact.name.ilike(search_pattern),
                Contact.phone.ilike(search_pattern),
                Contact.email.ilike(search_pattern),
                Contact.location.ilike(search_pattern),
                Contact.met_at.ilike(search_pattern),
                Contact.memory_prompt.ilike(search_pattern)
            )
        )

    # Platform filter
    if platform:
        db_query = db_query.filter(Contact.social_media.has_key(platform))

    # Date filters
    if date_from:
        db_query = db_query.filter(Contact.created_at >= date_from)
    if date_to:
        db_query = db_query.filter(Contact.created_at <= date_to)

    return db_query.order_by(Contact.name.asc()).offset(skip).limit(limit).all()

def get_contacts_by_ids(db: Session, user_id: str, contact_ids: List[str]) -> List[Contact]:
    """Get multiple contacts by their IDs"""
    return db.query(Contact).filter(
        and_(
            Contact.owner_id == user_id,
            Contact.id.in_(contact_ids),
            Contact.deleted_at.is_(None)
        )
    ).all()

def update_contact_sync_status(
    db: Session,
    contact_id: str,
    user_id: str,
    sync_status: str,
    device_contact_id: Optional[str] = None,
    sync_error: Optional[str] = None
) -> Optional[Contact]:
    """Update contact device sync status"""
    db_contact = get_contact_by_id(db, contact_id, user_id)
    if db_contact:
        db_contact.device_sync_status = sync_status
        if device_contact_id:
            db_contact.device_contact_id = device_contact_id
        if sync_status == 'synced':
            db_contact.synced_at = func.now()
        db.commit()
        db.refresh(db_contact)
    return db_contact

def get_contact_analytics(db: Session, user_id: str) -> Dict[str, Any]:
    """Get contact analytics for a user"""
    from datetime import datetime, timedelta
    from sqlalchemy import func, extract

    # Total contacts
    total_contacts = count_user_contacts(db, user_id)

    # Contacts this month
    current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    contacts_this_month = db.query(Contact).filter(
        and_(
            Contact.owner_id == user_id,
            Contact.created_at >= current_month,
            Contact.deleted_at.is_(None)
        )
    ).count()

    # Top social platforms
    social_platforms = db.query(Contact).filter(
        and_(Contact.owner_id == user_id, Contact.deleted_at.is_(None))
    ).all()

    platform_counts = {}
    location_counts = {}

    for contact in social_platforms:
        # Count social platforms
        if contact.social_media:
            for platform in contact.social_media.keys():
                platform_counts[platform] = platform_counts.get(platform, 0) + 1

        # Count locations
        if contact.location:
            location_counts[contact.location] = location_counts.get(contact.location, 0) + 1

    # Sort and get top platforms/locations
    top_platforms = [{"platform": k, "count": v} for k, v in
                    sorted(platform_counts.items(), key=lambda x: x[1], reverse=True)[:5]]
    top_locations = [{"location": k, "count": v} for k, v in
                    sorted(location_counts.items(), key=lambda x: x[1], reverse=True)[:5]]

    # Device sync stats
    sync_stats = db.query(Contact.device_sync_status, func.count(Contact.id)).filter(
        and_(Contact.owner_id == user_id, Contact.deleted_at.is_(None))
    ).group_by(Contact.device_sync_status).all()

    device_sync_stats = {status: count for status, count in sync_stats}

    return {
        "total_contacts": total_contacts,
        "contacts_this_month": contacts_this_month,
        "top_social_platforms": top_platforms,
        "top_locations": top_locations,
        "device_sync_stats": device_sync_stats
    }

# Social Platform CRUD operations

def get_social_platforms(db: Session, active_only: bool = True) -> List:
    """Get all social platforms"""
    from app.db.models import SocialPlatform
    query = db.query(SocialPlatform)
    if active_only:
        query = query.filter(SocialPlatform.is_active == True)
    return query.order_by(SocialPlatform.display_name.asc()).all()

def get_social_platform_by_name(db: Session, name: str) -> Optional:
    """Get a social platform by name"""
    from app.db.models import SocialPlatform
    return db.query(SocialPlatform).filter(SocialPlatform.name == name).first()

# Lesson CRUD operations
def create_lesson(
    db: Session,
    user_id: str,
    scenario: str,
    choice: str,
    lesson: str
) -> Lesson:
    """Create a new lesson for a user"""
    lesson_id = str(uuid.uuid4())
    db_lesson = Lesson(
        id=lesson_id,
        scenario=scenario.strip(),
        choice=choice.strip(),
        lesson=lesson.strip(),
        owner_id=user_id
    )
    db.add(db_lesson)
    db.commit()
    db.refresh(db_lesson)
    return db_lesson

def get_user_lessons(
    db: Session,
    user_id: str,
    skip: int = 0,
    limit: int = 100,
    search_query: Optional[str] = None
) -> List[Lesson]:
    """Get lessons for a user with optional search and pagination"""
    query = db.query(Lesson).filter(Lesson.owner_id == user_id)

    if search_query:
        # Search in scenario, choice, and lesson content
        search_pattern = f"%{search_query}%"
        query = query.filter(
            or_(
                Lesson.scenario.ilike(search_pattern),
                Lesson.choice.ilike(search_pattern),
                Lesson.lesson.ilike(search_pattern)
            )
        )

    return query.order_by(desc(Lesson.created_at)).offset(skip).limit(limit).all()

def get_lesson_by_id(db: Session, lesson_id: str, user_id: str) -> Optional[Lesson]:
    """Get a specific lesson by ID, ensuring it belongs to the user"""
    return db.query(Lesson).filter(
        and_(Lesson.id == lesson_id, Lesson.owner_id == user_id)
    ).first()

def update_lesson(db: Session, lesson_id: str, user_id: str, lesson_data: Dict[str, Any]) -> Optional[Lesson]:
    """Update a lesson's information"""
    db_lesson = get_lesson_by_id(db, lesson_id, user_id)
    if db_lesson:
        for field, value in lesson_data.items():
            if hasattr(db_lesson, field) and value:
                setattr(db_lesson, field, value.strip())
        db.commit()
        db.refresh(db_lesson)
    return db_lesson

def delete_lesson(db: Session, lesson_id: str, user_id: str) -> bool:
    """Delete a lesson, ensuring it belongs to the user"""
    db_lesson = get_lesson_by_id(db, lesson_id, user_id)
    if db_lesson:
        db.delete(db_lesson)
        db.commit()
        return True
    return False

def count_user_lessons(db: Session, user_id: str) -> int:
    """Count total lessons for a user"""
    return db.query(Lesson).filter(Lesson.owner_id == user_id).count()

# Notification Preference CRUD operations
def create_notification_preferences(db: Session, user_id: str) -> NotificationPreference:
    """Create default notification preferences for a user"""
    db_preferences = NotificationPreference(owner_id=user_id)
    db.add(db_preferences)
    db.commit()
    db.refresh(db_preferences)
    return db_preferences

def get_notification_preferences(db: Session, user_id: str) -> Optional[NotificationPreference]:
    """Get notification preferences for a user"""
    return db.query(NotificationPreference).filter(NotificationPreference.owner_id == user_id).first()

def get_or_create_notification_preferences(db: Session, user_id: str) -> NotificationPreference:
    """Get notification preferences for a user, creating default ones if they don't exist"""
    preferences = get_notification_preferences(db, user_id)
    if not preferences:
        preferences = create_notification_preferences(db, user_id)
    return preferences

def update_notification_preferences(
    db: Session,
    user_id: str,
    preferences_data: Dict[str, Any]
) -> Optional[NotificationPreference]:
    """Update notification preferences for a user"""
    db_preferences = get_notification_preferences(db, user_id)
    if not db_preferences:
        return None

    for key, value in preferences_data.items():
        if hasattr(db_preferences, key):
            setattr(db_preferences, key, value)

    db.commit()
    db.refresh(db_preferences)
    return db_preferences

def update_user_fcm_token(db: Session, user_id: str, fcm_token: str) -> Optional[User]:
    """Update FCM token for a user"""
    db_user = get_user_by_id(db, user_id)
    if not db_user:
        return None

    db_user.fcm_token = fcm_token
    db.commit()
    db.refresh(db_user)
    return db_user

# Notification History CRUD operations
def create_notification_history(
    db: Session,
    user_id: str,
    notification_type: str,
    title: str,
    content: str,
    relevance_score: float
) -> NotificationHistory:
    """Create a notification history record"""
    db_history = NotificationHistory(
        owner_id=user_id,
        notification_type=notification_type,
        title=title,
        content=content,
        relevance_score=relevance_score
    )
    db.add(db_history)
    db.commit()
    db.refresh(db_history)
    return db_history

def get_notification_history(
    db: Session,
    user_id: str,
    skip: int = 0,
    limit: int = 50,
    notification_type: Optional[str] = None
) -> List[NotificationHistory]:
    """Get notification history for a user with pagination"""
    query = db.query(NotificationHistory).filter(NotificationHistory.owner_id == user_id)

    if notification_type:
        query = query.filter(NotificationHistory.notification_type == notification_type)

    return query.order_by(desc(NotificationHistory.sent_at)).offset(skip).limit(limit).all()

def update_notification_interaction(
    db: Session,
    notification_id: str,
    interaction_type: str
) -> Optional[NotificationHistory]:
    """Update notification interaction (clicked or dismissed)"""
    db_notification = db.query(NotificationHistory).filter(NotificationHistory.id == notification_id).first()
    if not db_notification:
        return None

    current_time = datetime.utcnow()

    if interaction_type == "clicked":
        db_notification.clicked_at = current_time
    elif interaction_type == "dismissed":
        db_notification.dismissed_at = current_time

    db.commit()
    db.refresh(db_notification)
    return db_notification

def get_notification_analytics(db: Session, user_id: str, days: int = 30) -> Dict[str, Any]:
    """Get notification analytics for a user over the specified number of days"""
    from datetime import datetime, timedelta

    start_date = datetime.utcnow() - timedelta(days=days)

    # Get all notifications in the time period
    notifications = db.query(NotificationHistory).filter(
        and_(
            NotificationHistory.owner_id == user_id,
            NotificationHistory.sent_at >= start_date
        )
    ).all()

    total_sent = len(notifications)
    total_clicked = len([n for n in notifications if n.clicked_at])
    total_dismissed = len([n for n in notifications if n.dismissed_at])

    # Calculate click-through rate
    click_rate = (total_clicked / total_sent * 100) if total_sent > 0 else 0
    dismiss_rate = (total_dismissed / total_sent * 100) if total_sent > 0 else 0

    # Group by notification type
    type_stats = {}
    for notification in notifications:
        ntype = notification.notification_type
        if ntype not in type_stats:
            type_stats[ntype] = {"sent": 0, "clicked": 0, "dismissed": 0}

        type_stats[ntype]["sent"] += 1
        if notification.clicked_at:
            type_stats[ntype]["clicked"] += 1
        if notification.dismissed_at:
            type_stats[ntype]["dismissed"] += 1

    return {
        "total_sent": total_sent,
        "total_clicked": total_clicked,
        "total_dismissed": total_dismissed,
        "click_rate": round(click_rate, 2),
        "dismiss_rate": round(dismiss_rate, 2),
        "type_stats": type_stats,
        "period_days": days
    }


# Therapy Knowledge Chunk CRUD operations
def create_therapy_knowledge_chunk(
    db: Session,
    content: str,
    embedding: List[float],
    chapter: Optional[str] = None,
    section: Optional[str] = None,
    therapy_technique: Optional[str] = None,
    emotional_themes: Optional[List[str]] = None,
    keywords: Optional[List[str]] = None,
    chunk_index: int = 0,
    total_chunks: Optional[int] = None,
    source_document: Optional[str] = None
) -> TherapyKnowledgeChunk:
    """Create a new therapy knowledge chunk with embedding"""
    chunk_id = str(uuid.uuid4())
    db_chunk = TherapyKnowledgeChunk(
        id=chunk_id,
        content=content,
        embedding=embedding,
        chapter=chapter,
        section=section,
        therapy_technique=therapy_technique,
        emotional_themes=emotional_themes,
        keywords=keywords,
        chunk_index=chunk_index,
        total_chunks=total_chunks,
        source_document=source_document
    )
    db.add(db_chunk)
    db.commit()
    db.refresh(db_chunk)
    return db_chunk

def search_therapy_knowledge(
    db: Session,
    query_embedding: List[float],
    therapy_technique: Optional[str] = None,
    emotional_themes: Optional[List[str]] = None,
    limit: int = 5
) -> List[TherapyKnowledgeChunk]:
    """Search therapy knowledge using vector similarity and optional filters"""
    query = db.query(TherapyKnowledgeChunk).order_by(
        TherapyKnowledgeChunk.embedding.cosine_distance(query_embedding)
    )

    # Apply filters if provided
    if therapy_technique:
        query = query.filter(TherapyKnowledgeChunk.therapy_technique.ilike(f"%{therapy_technique}%"))

    if emotional_themes:
        # Filter by emotional themes (JSON array contains any of the specified themes)
        for theme in emotional_themes:
            query = query.filter(TherapyKnowledgeChunk.emotional_themes.contains([theme]))

    return query.limit(limit).all()

def update_therapy_chunk_effectiveness(
    db: Session,
    chunk_id: str,
    effectiveness_score: float
) -> bool:
    """Update the effectiveness score of a therapy knowledge chunk"""
    chunk = db.query(TherapyKnowledgeChunk).filter(TherapyKnowledgeChunk.id == chunk_id).first()
    if chunk:
        chunk.retrieval_count += 1
        chunk.effectiveness_score = effectiveness_score
        db.commit()
        return True
    return False

def get_therapy_knowledge_by_technique(
    db: Session,
    therapy_technique: str,
    limit: int = 10
) -> List[TherapyKnowledgeChunk]:
    """Get therapy knowledge chunks by specific technique"""
    return db.query(TherapyKnowledgeChunk).filter(
        TherapyKnowledgeChunk.therapy_technique.ilike(f"%{therapy_technique}%")
    ).order_by(TherapyKnowledgeChunk.effectiveness_score.desc()).limit(limit).all()


# Therapy Session CRUD operations
def create_therapy_session(
    db: Session,
    conversation_id: str,
    user_id: str,
    session_mode: str = "chat",
    session_focus: Optional[str] = None,
    session_goals: Optional[List[str]] = None
) -> TherapySession:
    """Create a new therapy session"""
    session_id = str(uuid.uuid4())
    db_session = TherapySession(
        id=session_id,
        conversation_id=conversation_id,
        user_id=user_id,
        session_mode=session_mode,
        current_modality=session_mode,
        session_focus=session_focus,
        session_goals=session_goals
    )
    db.add(db_session)
    db.commit()
    db.refresh(db_session)
    return db_session

def get_therapy_session_by_conversation(
    db: Session,
    conversation_id: str
) -> Optional[TherapySession]:
    """Get active therapy session by conversation ID"""
    return db.query(TherapySession).filter(
        and_(
            TherapySession.conversation_id == conversation_id,
            TherapySession.ended_at.is_(None)
        )
    ).first()

def update_therapy_session_modality(
    db: Session,
    session_id: str,
    new_modality: str
) -> bool:
    """Update the current modality of a therapy session"""
    session = db.query(TherapySession).filter(TherapySession.id == session_id).first()
    if session:
        if session.current_modality != new_modality:
            session.modality_switches += 1
        session.current_modality = new_modality
        db.commit()
        return True
    return False

def update_therapy_session_emotional_state(
    db: Session,
    session_id: str,
    emotional_state: Dict[str, Any]
) -> bool:
    """Update the emotional state tracking for a therapy session"""
    session = db.query(TherapySession).filter(TherapySession.id == session_id).first()
    if session:
        if not session.initial_emotional_state:
            session.initial_emotional_state = emotional_state

        # Add to emotional progression
        if not session.emotional_progression:
            session.emotional_progression = []

        session.emotional_progression.append({
            "timestamp": datetime.utcnow().isoformat(),
            "state": emotional_state
        })
        session.current_emotional_state = emotional_state
        db.commit()
        return True
    return False

def end_therapy_session(
    db: Session,
    session_id: str,
    key_insights: Optional[List[str]] = None,
    practice_tasks: Optional[List[str]] = None
) -> bool:
    """End a therapy session and record final data"""
    session = db.query(TherapySession).filter(TherapySession.id == session_id).first()
    if session:
        session.ended_at = datetime.utcnow()
        session.key_insights = key_insights
        session.practice_tasks_assigned = practice_tasks

        # Calculate session duration
        if session.started_at:
            duration = session.ended_at - session.started_at
            session.session_duration_minutes = int(duration.total_seconds() / 60)

        db.commit()
        return True
    return False

def get_user_therapy_sessions(
    db: Session,
    user_id: str,
    limit: int = 10,
    include_active: bool = True
) -> List[TherapySession]:
    """Get therapy sessions for a user"""
    query = db.query(TherapySession).filter(TherapySession.user_id == user_id)

    if not include_active:
        query = query.filter(TherapySession.ended_at.isnot(None))

    return query.order_by(desc(TherapySession.started_at)).limit(limit).all()


# Therapy Progress CRUD operations
def create_therapy_progress_entry(
    db: Session,
    user_id: str,
    progress_type: str,
    content: str,
    session_id: Optional[str] = None,
    mood_score: Optional[int] = None,
    anxiety_level: Optional[int] = None,
    emotional_tags: Optional[List[str]] = None,
    task_name: Optional[str] = None,
    task_completed: bool = False,
    intervention_used: Optional[str] = None,
    user_feedback_rating: Optional[int] = None
) -> TherapyProgress:
    """Create a new therapy progress entry"""
    progress_id = str(uuid.uuid4())
    db_progress = TherapyProgress(
        id=progress_id,
        user_id=user_id,
        session_id=session_id,
        progress_type=progress_type,
        content=content,
        mood_score=mood_score,
        anxiety_level=anxiety_level,
        emotional_tags=emotional_tags,
        task_name=task_name,
        task_completed=task_completed,
        intervention_used=intervention_used,
        user_feedback_rating=user_feedback_rating
    )
    db.add(db_progress)
    db.commit()
    db.refresh(db_progress)
    return db_progress

def get_user_therapy_progress(
    db: Session,
    user_id: str,
    progress_type: Optional[str] = None,
    limit: int = 50
) -> List[TherapyProgress]:
    """Get therapy progress entries for a user"""
    query = db.query(TherapyProgress).filter(TherapyProgress.user_id == user_id)

    if progress_type:
        query = query.filter(TherapyProgress.progress_type == progress_type)

    return query.order_by(desc(TherapyProgress.created_at)).limit(limit).all()

def update_therapy_progress_task_completion(
    db: Session,
    progress_id: str,
    completed: bool,
    effectiveness_rating: Optional[int] = None,
    notes: Optional[str] = None
) -> bool:
    """Update task completion status and effectiveness"""
    progress = db.query(TherapyProgress).filter(TherapyProgress.id == progress_id).first()
    if progress:
        progress.task_completed = completed
        if effectiveness_rating is not None:
            progress.task_effectiveness_rating = effectiveness_rating
        if notes is not None:
            progress.task_notes = notes
        db.commit()
        return True
    return False

def get_therapy_progress_analytics(
    db: Session,
    user_id: str,
    days: int = 30
) -> Dict[str, Any]:
    """Get therapy progress analytics for a user"""
    from datetime import datetime, timedelta

    start_date = datetime.utcnow() - timedelta(days=days)

    # Get all progress entries in the time period
    progress_entries = db.query(TherapyProgress).filter(
        and_(
            TherapyProgress.user_id == user_id,
            TherapyProgress.created_at >= start_date
        )
    ).all()

    # Calculate mood trends
    mood_entries = [p for p in progress_entries if p.mood_score is not None]
    avg_mood = sum(p.mood_score for p in mood_entries) / len(mood_entries) if mood_entries else 0

    # Calculate task completion rate
    task_entries = [p for p in progress_entries if p.progress_type == "practice_task"]
    completed_tasks = [p for p in task_entries if p.task_completed]
    completion_rate = (len(completed_tasks) / len(task_entries) * 100) if task_entries else 0

    # Most effective interventions
    intervention_ratings = {}
    for entry in progress_entries:
        if entry.intervention_used and entry.user_feedback_rating:
            if entry.intervention_used not in intervention_ratings:
                intervention_ratings[entry.intervention_used] = []
            intervention_ratings[entry.intervention_used].append(entry.user_feedback_rating)

    # Calculate average ratings for each intervention
    intervention_effectiveness = {}
    for intervention, ratings in intervention_ratings.items():
        intervention_effectiveness[intervention] = sum(ratings) / len(ratings)

    return {
        "total_entries": len(progress_entries),
        "average_mood": round(avg_mood, 2),
        "task_completion_rate": round(completion_rate, 2),
        "total_tasks": len(task_entries),
        "completed_tasks": len(completed_tasks),
        "intervention_effectiveness": intervention_effectiveness,
        "period_days": days
    }

# Calendar Event CRUD operations
def create_calendar_event(
    db: Session,
    user_id: str,
    title: str,
    start_datetime: datetime,
    end_datetime: datetime,
    description: Optional[str] = None,
    is_all_day: bool = False,
    timezone: str = "UTC",
    is_recurring: bool = False,
    recurrence_rule: Optional[str] = None,
    recurrence_end_date: Optional[datetime] = None,
    category: Optional[str] = None,
    tags: Optional[List[str]] = None,
    color: Optional[str] = None,
    location: Optional[str] = None,
    location_data: Optional[Dict[str, Any]] = None,
    reminder_minutes_before: Optional[List[int]] = None,
    attendees: Optional[List[Dict[str, Any]]] = None
) -> CalendarEvent:
    """Create a new calendar event for a user"""
    event_id = str(uuid.uuid4())
    db_event = CalendarEvent(
        id=event_id,
        title=title,
        description=description,
        start_datetime=start_datetime,
        end_datetime=end_datetime,
        is_all_day=is_all_day,
        timezone=timezone,
        is_recurring=is_recurring,
        recurrence_rule=recurrence_rule,
        recurrence_end_date=recurrence_end_date,
        category=category,
        tags=tags or [],
        color=color,
        location=location,
        location_data=location_data,
        reminder_minutes_before=reminder_minutes_before or [],
        attendees=attendees or [],
        owner_id=user_id,
        created_by_user_id=user_id
    )
    db.add(db_event)
    db.commit()
    db.refresh(db_event)
    return db_event

def get_user_calendar_events(
    db: Session,
    user_id: str,
    skip: int = 0,
    limit: int = 100,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    category: Optional[str] = None,
    tags: Optional[List[str]] = None,
    is_recurring: Optional[bool] = None
) -> List[CalendarEvent]:
    """Get calendar events for a user with filtering"""
    query = db.query(CalendarEvent).filter(CalendarEvent.owner_id == user_id)

    # Apply filters
    if start_date:
        query = query.filter(CalendarEvent.start_datetime >= start_date)

    if end_date:
        query = query.filter(CalendarEvent.end_datetime <= end_date)

    if category:
        query = query.filter(CalendarEvent.category == category)

    if is_recurring is not None:
        query = query.filter(CalendarEvent.is_recurring == is_recurring)

    if tags:
        # Filter events that contain any of the specified tags
        tag_conditions = [CalendarEvent.tags.contains([tag]) for tag in tags]
        query = query.filter(or_(*tag_conditions))

    return query.order_by(CalendarEvent.start_datetime).offset(skip).limit(limit).all()

def get_calendar_event_by_id(db: Session, event_id: str, user_id: str) -> Optional[CalendarEvent]:
    """Get a specific calendar event by ID, ensuring it belongs to the user"""
    return db.query(CalendarEvent).filter(
        and_(CalendarEvent.id == event_id, CalendarEvent.owner_id == user_id)
    ).first()

def update_calendar_event(db: Session, event_id: str, user_id: str, event_data: Dict[str, Any]) -> Optional[CalendarEvent]:
    """Update a calendar event"""
    db_event = get_calendar_event_by_id(db, event_id, user_id)
    if db_event:
        for field, value in event_data.items():
            if hasattr(db_event, field):
                setattr(db_event, field, value)
        db.commit()
        db.refresh(db_event)
    return db_event

def delete_calendar_event(db: Session, event_id: str, user_id: str) -> bool:
    """Delete a calendar event, ensuring it belongs to the user"""
    db_event = get_calendar_event_by_id(db, event_id, user_id)
    if db_event:
        db.delete(db_event)
        db.commit()
        return True
    return False

def count_user_calendar_events(db: Session, user_id: str) -> int:
    """Count total calendar events for a user"""
    return db.query(CalendarEvent).filter(CalendarEvent.owner_id == user_id).count()

def get_events_for_date_range(
    db: Session,
    user_id: str,
    start_date: datetime,
    end_date: datetime
) -> List[CalendarEvent]:
    """Get events within a specific date range"""
    return db.query(CalendarEvent).filter(
        and_(
            CalendarEvent.owner_id == user_id,
            CalendarEvent.start_datetime >= start_date,
            CalendarEvent.start_datetime <= end_date
        )
    ).order_by(CalendarEvent.start_datetime).all()

# Enhanced Task CRUD operations for subtasks and time tracking
def create_subtask(
    db: Session,
    user_id: str,
    parent_task_id: str,
    content: str,
    priority: int = 0,
    tags: Optional[List[str]] = None
) -> Optional[Task]:
    """Create a subtask under a parent task"""
    # Verify parent task exists and belongs to user
    parent_task = get_task_by_id(db, parent_task_id, user_id)
    if not parent_task:
        return None

    return create_user_task(
        db=db,
        user_id=user_id,
        content=content,
        priority=priority,
        tags=tags,
        parent_task_id=parent_task_id
    )

def get_subtasks(db: Session, parent_task_id: str, user_id: str) -> List[Task]:
    """Get all subtasks for a parent task"""
    return db.query(Task).filter(
        and_(
            Task.parent_task_id == parent_task_id,
            Task.owner_id == user_id
        )
    ).order_by(desc(Task.priority), Task.created_at).all()

def start_time_tracking(db: Session, task_id: str, user_id: str) -> Optional[Task]:
    """Start time tracking for a task"""
    task = get_task_by_id(db, task_id, user_id)
    if task and not task.time_tracking_started_at:
        task.time_tracking_started_at = datetime.utcnow()
        db.commit()
        db.refresh(task)
    return task

def stop_time_tracking(db: Session, task_id: str, user_id: str) -> Optional[Task]:
    """Stop time tracking for a task and calculate duration"""
    task = get_task_by_id(db, task_id, user_id)
    if task and task.time_tracking_started_at:
        end_time = datetime.utcnow()
        duration = end_time - task.time_tracking_started_at
        task.actual_duration_minutes = int(duration.total_seconds() / 60)
        task.time_tracking_started_at = None
        db.commit()
        db.refresh(task)
    return task

def get_task_dependencies(db: Session, task_id: str, user_id: str) -> List[Task]:
    """Get tasks that this task depends on"""
    task = get_task_by_id(db, task_id, user_id)
    if not task or not task.depends_on_task_ids:
        return []

    return db.query(Task).filter(
        and_(
            Task.id.in_(task.depends_on_task_ids),
            Task.owner_id == user_id
        )
    ).all()

def get_dependent_tasks(db: Session, task_id: str, user_id: str) -> List[Task]:
    """Get tasks that depend on this task"""
    return db.query(Task).filter(
        and_(
            Task.depends_on_task_ids.contains([task_id]),
            Task.owner_id == user_id
        )
    ).all()

def can_complete_task(db: Session, task_id: str, user_id: str) -> bool:
    """Check if a task can be completed (all dependencies are completed)"""
    dependencies = get_task_dependencies(db, task_id, user_id)
    return all(dep.is_completed for dep in dependencies)

# Attachment CRUD operations
def create_attachment(
    db: Session,
    user_id: str,
    filename: str,
    original_filename: str,
    file_size: int,
    mime_type: str,
    storage_url: str,
    storage_public_id: Optional[str] = None,
    storage_provider: str = "cloudinary",
    note_id: Optional[str] = None,
    task_id: Optional[str] = None,
    calendar_event_id: Optional[str] = None
) -> Attachment:
    """Create a new attachment"""
    attachment_id = str(uuid.uuid4())
    db_attachment = Attachment(
        id=attachment_id,
        filename=filename,
        original_filename=original_filename,
        file_size=file_size,
        mime_type=mime_type,
        storage_provider=storage_provider,
        storage_url=storage_url,
        storage_public_id=storage_public_id,
        note_id=note_id,
        task_id=task_id,
        calendar_event_id=calendar_event_id,
        owner_id=user_id
    )
    db.add(db_attachment)
    db.commit()
    db.refresh(db_attachment)
    return db_attachment

def get_attachments_for_entity(
    db: Session,
    user_id: str,
    entity_type: str,
    entity_id: str
) -> List[Attachment]:
    """Get attachments for a specific entity (note, task, or calendar event)"""
    query = db.query(Attachment).filter(Attachment.owner_id == user_id)

    if entity_type == "note":
        query = query.filter(Attachment.note_id == entity_id)
    elif entity_type == "task":
        query = query.filter(Attachment.task_id == entity_id)
    elif entity_type == "calendar_event":
        query = query.filter(Attachment.calendar_event_id == entity_id)
    else:
        return []

    return query.order_by(desc(Attachment.created_at)).all()

def delete_attachment(db: Session, attachment_id: str, user_id: str) -> bool:
    """Delete an attachment, ensuring it belongs to the user"""
    attachment = db.query(Attachment).filter(
        and_(Attachment.id == attachment_id, Attachment.owner_id == user_id)
    ).first()

    if attachment:
        db.delete(attachment)
        db.commit()
        return True
    return False

# Sync Status CRUD operations
def create_sync_status(
    db: Session,
    user_id: str,
    entity_type: str,
    entity_id: str,
    device_id: str,
    sync_version: int = 1
) -> SyncStatus:
    """Create or update sync status for an entity"""
    # Check if sync status already exists
    existing = db.query(SyncStatus).filter(
        and_(
            SyncStatus.entity_type == entity_type,
            SyncStatus.entity_id == entity_id,
            SyncStatus.device_id == device_id,
            SyncStatus.owner_id == user_id
        )
    ).first()

    if existing:
        existing.last_sync_at = datetime.utcnow()
        existing.sync_version = sync_version
        existing.sync_status = "synced"
        db.commit()
        db.refresh(existing)
        return existing

    sync_id = str(uuid.uuid4())
    db_sync = SyncStatus(
        id=sync_id,
        entity_type=entity_type,
        entity_id=entity_id,
        device_id=device_id,
        last_sync_at=datetime.utcnow(),
        sync_version=sync_version,
        sync_status="synced",
        owner_id=user_id
    )
    db.add(db_sync)
    db.commit()
    db.refresh(db_sync)
    return db_sync

def get_sync_conflicts(db: Session, user_id: str, device_id: str) -> List[SyncStatus]:
    """Get all sync conflicts for a user and device"""
    return db.query(SyncStatus).filter(
        and_(
            SyncStatus.owner_id == user_id,
            SyncStatus.device_id == device_id,
            SyncStatus.has_conflicts == True
        )
    ).all()

def resolve_sync_conflict(
    db: Session,
    sync_id: str,
    user_id: str,
    resolution_data: Dict[str, Any]
) -> Optional[SyncStatus]:
    """Resolve a sync conflict"""
    sync_status = db.query(SyncStatus).filter(
        and_(SyncStatus.id == sync_id, SyncStatus.owner_id == user_id)
    ).first()

    if sync_status:
        sync_status.has_conflicts = False
        sync_status.conflict_data = resolution_data
        sync_status.resolved_at = datetime.utcnow()
        sync_status.sync_status = "synced"
        db.commit()
        db.refresh(sync_status)

    return sync_status