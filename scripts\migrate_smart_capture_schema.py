#!/usr/bin/env python3
"""
Smart Capture Schema Migration Script

This script adds the enhanced fields required for Smart Capture functionality
to the existing inbox_items table. It includes proper error handling and
rollback capabilities.

Usage:
    python scripts/migrate_smart_capture_schema.py

Environment Variables Required:
    DATABASE_URL - PostgreSQL connection string
"""

import os
import sys
import logging
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, DateTime, ARRAY, JSON
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime

# Try to load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("python-dotenv not available, using system environment variables")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_database_url():
    """Get database URL from environment variables."""
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        logger.error("DATABASE_URL environment variable not set")
        sys.exit(1)
    return database_url

def check_table_exists(engine, table_name):
    """Check if a table exists in the database."""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = :table_name
                );
            """), {"table_name": table_name})
            return result.scalar()
    except SQLAlchemyError as e:
        logger.error(f"Error checking table existence: {e}")
        return False

def check_column_exists(engine, table_name, column_name):
    """Check if a column exists in a table."""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = :table_name 
                    AND column_name = :column_name
                );
            """), {"table_name": table_name, "column_name": column_name})
            return result.scalar()
    except SQLAlchemyError as e:
        logger.error(f"Error checking column existence: {e}")
        return False

def add_smart_capture_columns(engine):
    """Add Smart Capture columns to inbox_items table."""
    columns_to_add = [
        ("title", "VARCHAR(500)"),
        ("author", "VARCHAR(200)"),
        ("publish_date", "TIMESTAMP WITH TIME ZONE"),
        ("word_count", "INTEGER"),
        ("reading_time", "INTEGER"),
        ("thumbnail_url", "TEXT"),
        ("tags", "TEXT[]"),
        ("category", "VARCHAR(50)"),
        ("content_type", "VARCHAR(50)"),
        ("metadata", "JSONB"),
        ("processing_error", "TEXT"),
        ("retry_count", "INTEGER DEFAULT 0")
    ]
    
    try:
        with engine.connect() as conn:
            # Start transaction
            trans = conn.begin()
            
            for column_name, column_type in columns_to_add:
                if not check_column_exists(engine, "inbox_items", column_name):
                    logger.info(f"Adding column: {column_name}")
                    conn.execute(text(f"""
                        ALTER TABLE inbox_items 
                        ADD COLUMN {column_name} {column_type};
                    """))
                else:
                    logger.info(f"Column {column_name} already exists, skipping")
            
            # Commit transaction
            trans.commit()
            logger.info("Successfully added Smart Capture columns")
            
    except SQLAlchemyError as e:
        logger.error(f"Error adding columns: {e}")
        trans.rollback()
        raise

def create_search_indexes(engine):
    """Create indexes for improved search performance."""
    indexes_to_create = [
        ("idx_inbox_items_tags", "inbox_items", "USING GIN(tags)"),
        ("idx_inbox_items_category", "inbox_items", "(category)"),
        ("idx_inbox_items_content_type", "inbox_items", "(content_type)"),
        ("idx_inbox_items_title_search", "inbox_items", "USING GIN(to_tsvector('english', title))"),
        ("idx_inbox_items_content_search", "inbox_items", "USING GIN(to_tsvector('english', clean_content))"),
        ("idx_inbox_items_created_at", "inbox_items", "(created_at DESC)"),
        ("idx_inbox_items_user_status", "inbox_items", "(owner_id, status)"),
        ("idx_inbox_items_publish_date", "inbox_items", "(publish_date DESC)"),
        ("idx_inbox_items_word_count", "inbox_items", "(word_count)"),
        ("idx_inbox_items_reading_time", "inbox_items", "(reading_time)")
    ]

    try:
        # Use autocommit mode for CONCURRENT index creation
        with engine.connect() as conn:
            conn.execute(text("COMMIT"))  # End any existing transaction

            for index_name, table_name, index_definition in indexes_to_create:
                # Check if index already exists
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM pg_indexes
                        WHERE schemaname = 'public'
                        AND tablename = :table_name
                        AND indexname = :index_name
                    );
                """), {"table_name": table_name, "index_name": index_name})

                if not result.scalar():
                    logger.info(f"Creating index: {index_name}")
                    # Use regular CREATE INDEX instead of CONCURRENT for simplicity
                    conn.execute(text(f"""
                        CREATE INDEX {index_name}
                        ON {table_name} {index_definition};
                    """))
                else:
                    logger.info(f"Index {index_name} already exists, skipping")

    except SQLAlchemyError as e:
        logger.error(f"Error creating indexes: {e}")
        raise

def verify_migration(engine):
    """Verify that the migration was successful."""
    required_columns = [
        "title", "author", "publish_date", "word_count", "reading_time",
        "thumbnail_url", "tags", "category", "content_type", "metadata",
        "processing_error", "retry_count"
    ]
    
    missing_columns = []
    for column in required_columns:
        if not check_column_exists(engine, "inbox_items", column):
            missing_columns.append(column)
    
    if missing_columns:
        logger.error(f"Migration verification failed. Missing columns: {missing_columns}")
        return False
    
    logger.info("Migration verification successful. All columns present.")
    return True

def main():
    """Main migration function."""
    logger.info("Starting Smart Capture schema migration")
    
    # Get database connection
    database_url = get_database_url()
    engine = create_engine(database_url)
    
    try:
        # Check if inbox_items table exists
        if not check_table_exists(engine, "inbox_items"):
            logger.error("inbox_items table does not exist. Please run base migrations first.")
            sys.exit(1)
        
        # Add Smart Capture columns
        logger.info("Adding Smart Capture columns to inbox_items table")
        add_smart_capture_columns(engine)
        
        # Create search indexes
        logger.info("Creating search indexes for improved performance")
        create_search_indexes(engine)
        
        # Verify migration
        if verify_migration(engine):
            logger.info("Smart Capture schema migration completed successfully")
        else:
            logger.error("Migration verification failed")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        sys.exit(1)
    finally:
        engine.dispose()

if __name__ == "__main__":
    main()
