# Dashboard API Documentation

## Overview

The Dashboard API provides aggregated data for the Darvis mobile app's home screen in a single optimized endpoint. This reduces the number of API calls needed and provides a consistent data structure for frontend consumption.

## Endpoint

### GET `/api/v1/dashboard/daily-summary`

**Description:** Get comprehensive dashboard data for the authenticated user's home screen.

**Authentication:** Required (Firebase ID token)

**Response Time:** Optimized for mobile (~200-500ms)

## Request

```http
GET /api/v1/dashboard/daily-summary
Authorization: Bearer <firebase_id_token>
Content-Type: application/json
```

## Response Structure

```json
{
  "user_profile": {
    "id": "firebase_user_id",
    "email": "<EMAIL>",
    "display_name": "<PERSON>",
    "profile_image_url": "https://example.com/avatar.jpg",
    "created_at": "2024-01-15T10:30:00Z",
    "member_since_days": 45
  },
  "greeting": {
    "greeting_message": "Good morning, <PERSON>! Ready to tackle the day?",
    "time_of_day": "morning",
    "is_first_login_today": true,
    "login_count_today": 1,
    "current_streak": 7
  },
  "quick_stats": {
    "total_tasks": 25,
    "completed_tasks_today": 3,
    "pending_tasks": 8,
    "overdue_tasks": 2,
    "total_notes": 15,
    "notes_created_today": 1
  },
  "today_tasks": [
    {
      "id": "task_uuid",
      "content": "Review project proposal",
      "is_completed": false,
      "due_date": "2024-01-28T17:00:00Z",
      "priority": 2,
      "tags": ["work", "urgent"],
      "is_overdue": false
    }
  ],
  "recent_notes": [
    {
      "id": "note_uuid",
      "title": "Meeting Notes",
      "content": "Discussed project timeline and deliverables...",
      "tags": ["work", "meeting"],
      "created_at": "2024-01-28T09:15:00Z",
      "updated_at": "2024-01-28T09:20:00Z"
    }
  ],
  "generated_at": "2024-01-28T10:30:00Z"
}
```

## Data Components

### User Profile
- **id**: Firebase user ID
- **email**: User's email address
- **display_name**: User's display name (optional, falls back to email prefix)
- **profile_image_url**: Profile picture URL (optional)
- **created_at**: Account creation timestamp
- **member_since_days**: Days since account creation

### Greeting Data
- **greeting_message**: Time-based personalized greeting
- **time_of_day**: "morning", "afternoon", "evening", or "night"
- **is_first_login_today**: Boolean indicating first login of the day
- **login_count_today**: Number of logins today
- **current_streak**: Consecutive days with at least one login

### Quick Stats
- **total_tasks**: Total number of user's tasks
- **completed_tasks_today**: Tasks completed today
- **pending_tasks**: Incomplete tasks due today or overdue
- **overdue_tasks**: Tasks past due date and not completed
- **total_notes**: Total number of user's notes
- **notes_created_today**: Notes created today

### Today's Tasks
Array of up to 10 most important tasks (due today + overdue):
- **id**: Task UUID
- **content**: Task description
- **is_completed**: Completion status
- **due_date**: Due date (ISO 8601)
- **priority**: 0=low, 1=medium, 2=high, 3=urgent
- **tags**: Array of tag strings
- **is_overdue**: Boolean indicating if task is past due

### Recent Notes
Array of up to 5 most recent notes:
- **id**: Note UUID
- **title**: Note title (optional)
- **content**: Note content (truncated to 200 chars)
- **tags**: Array of tag strings
- **created_at**: Creation timestamp
- **updated_at**: Last update timestamp

## Time-Based Greetings

The greeting message changes based on the current time:

- **5:00 - 11:59**: "Good morning, {name}! Ready to tackle the day?"
- **12:00 - 16:59**: "Good afternoon, {name}! How's your day going?"
- **17:00 - 20:59**: "Good evening, {name}! Winding down or still productive?"
- **21:00 - 4:59**: "Good night, {name}! Late night productivity session?"

## Session Tracking

The API automatically tracks user logins using the `ActionLog` table:
- Records each dashboard access as a login event
- Calculates daily login count
- Determines if this is the first login of the day
- Computes login streak (consecutive days with at least one login)

## Error Responses

### 401 Unauthorized
```json
{
  "detail": "Not authenticated"
}
```

### 404 Not Found
```json
{
  "detail": "User not found"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Failed to generate dashboard summary"
}
```

## Performance Optimizations

1. **Single Query for Today's Tasks**: Combines due today and overdue tasks in one database query
2. **Count Queries**: Uses optimized COUNT queries instead of fetching all records
3. **Limited Results**: Caps tasks (10) and notes (5) to prevent large responses
4. **Efficient Sorting**: Tasks sorted by completion status, priority, and due date
5. **Minimal Data Transfer**: Note content truncated to 200 characters

## Usage Examples

### Frontend Integration (React Native/Flutter)

```javascript
// Fetch dashboard data
const fetchDashboard = async () => {
  try {
    const token = await getFirebaseToken();
    const response = await fetch('/api/v1/dashboard/daily-summary', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      updateHomeScreen(data);
    }
  } catch (error) {
    console.error('Dashboard fetch failed:', error);
  }
};
```

### Home Screen Components

```javascript
// Use the structured data for different UI components
const HomeScreen = ({ dashboardData }) => {
  return (
    <View>
      <GreetingCard greeting={dashboardData.greeting} />
      <StatsOverview stats={dashboardData.quick_stats} />
      <TasksList tasks={dashboardData.today_tasks} />
      <RecentNotes notes={dashboardData.recent_notes} />
    </View>
  );
};
```

## Testing

Use the provided test script to verify the endpoint:

```bash
python test_dashboard.py
```

The test script checks:
- Server connectivity
- Authentication requirements
- Response structure
- Data completeness

## Database Dependencies

The dashboard endpoint requires these tables:
- `users` - User profile data
- `tasks` - Task management
- `notes` - Note storage
- `action_logs` - Session tracking

All tables must exist and be properly indexed for optimal performance.
