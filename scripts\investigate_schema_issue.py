#!/usr/bin/env python3
"""
<PERSON><PERSON>t to investigate the database schema issue with metadata columns.

This script will check the current state of the inbox_items table and identify
the metadata column situation.
"""

import os
import sys
from dotenv import load_dotenv
import psycopg2

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

def get_database_connection():
    """Get database connection from environment variables."""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError("DATABASE_URL environment variable not set")
    
    # Parse the database URL
    if database_url.startswith("postgresql://"):
        database_url = database_url.replace("postgresql://", "postgres://")
    
    return psycopg2.connect(database_url)

def investigate_schema():
    """Investigate the current database schema."""
    conn = None
    cursor = None
    
    try:
        print("=== Database Schema Investigation ===")
        print("Connecting to database...")
        conn = get_database_connection()
        cursor = conn.cursor()
        
        # Check all columns in inbox_items table
        print("\n1. ALL COLUMNS IN inbox_items TABLE:")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'inbox_items'
            ORDER BY ordinal_position
        """)
        
        columns = cursor.fetchall()
        for column_name, data_type, is_nullable, column_default in columns:
            print(f"  - {column_name}: {data_type} (nullable: {is_nullable}, default: {column_default})")
        
        # Check specifically for metadata columns
        print("\n2. METADATA COLUMNS ANALYSIS:")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'inbox_items'
            AND column_name LIKE '%metadata%'
            ORDER BY column_name
        """)
        
        metadata_columns = cursor.fetchall()
        if metadata_columns:
            print("Found metadata columns:")
            for column_name, data_type, is_nullable in metadata_columns:
                print(f"  - {column_name}: {data_type} (nullable: {is_nullable})")
        else:
            print("No metadata columns found")
        
        # Check indexes
        print("\n3. INDEXES ON inbox_items TABLE:")
        cursor.execute("""
            SELECT indexname, indexdef
            FROM pg_indexes 
            WHERE tablename = 'inbox_items'
            ORDER BY indexname
        """)
        
        indexes = cursor.fetchall()
        for index_name, index_def in indexes:
            print(f"  - {index_name}: {index_def}")
        
        # Check for any data in metadata columns
        if metadata_columns:
            print("\n4. DATA ANALYSIS:")
            for column_name, _, _ in metadata_columns:
                cursor.execute(f"""
                    SELECT COUNT(*) as total_rows,
                           COUNT({column_name}) as non_null_rows
                    FROM inbox_items
                """)
                total, non_null = cursor.fetchone()
                print(f"  - {column_name}: {non_null}/{total} rows have data")
        
        # Check table constraints
        print("\n5. TABLE CONSTRAINTS:")
        cursor.execute("""
            SELECT constraint_name, constraint_type
            FROM information_schema.table_constraints
            WHERE table_name = 'inbox_items'
            ORDER BY constraint_name
        """)
        
        constraints = cursor.fetchall()
        for constraint_name, constraint_type in constraints:
            print(f"  - {constraint_name}: {constraint_type}")
        
        print("\n=== INVESTIGATION COMPLETE ===")
        
    except Exception as e:
        print(f"❌ Error during investigation: {e}")
        raise
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def recommend_solution():
    """Recommend solution based on investigation."""
    print("\n=== RECOMMENDED SOLUTION ===")
    print("Based on the investigation, here are the recommended actions:")
    print()
    print("1. If both 'metadata' and 'item_metadata' columns exist:")
    print("   - Drop the 'metadata' column (SQLAlchemy conflict)")
    print("   - Keep 'item_metadata' column")
    print("   - Update code to use 'item_metadata' consistently")
    print()
    print("2. If only 'metadata' exists:")
    print("   - Rename 'metadata' to 'item_metadata'")
    print()
    print("3. If only 'item_metadata' exists:")
    print("   - Schema is correct, update code references")
    print()
    print("4. Migration script to fix the issue will be provided.")

def main():
    """Main investigation function."""
    try:
        investigate_schema()
        recommend_solution()
        return 0
    except Exception as e:
        print(f"\n💥 Investigation failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
