from sqlalchemy import Column, String, DateTime, Foreign<PERSON>ey, Text, Boolean, JSON, Integer, Float, ARRAY
from sqlalchemy.orm import relationship, declarative_base, mapped_column
from sqlalchemy.sql import func
from pgvector.sqlalchemy import Vector
import uuid

# Create a base class for our declarative models
Base = declarative_base()

class User(Base):
    __tablename__ = "users"

    # Use the Firebase UID as the primary key
    id = Column(String, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    display_name = Column(String, nullable=True)

    # Profile information
    darvis_name = Column(String, nullable=True)  # What should Drix call you?
    profile_picture_url = Column(String, nullable=True)  # Cloudinary URL
    profile_picture_public_id = Column(String, nullable=True)  # Cloudinary public ID for deletion

    # Legacy field for backward compatibility
    profile_image_url = Column(String, nullable=True)

    # Account information
    member_since = Column(DateTime(timezone=True), nullable=True)
    current_streak = Column(Integer, default=0)
    last_activity_date = Column(DateTime(timezone=True), nullable=True)

    # Subscription information
    subscription_plan = Column(String, default='Essential')  # 'Essential' or 'Unlimited'
    subscription_expiry_date = Column(DateTime(timezone=True), nullable=True)
    is_subscription_active = Column(Boolean, default=False)

    # User preferences
    time_zone = Column(String, default='UTC')
    language = Column(String, default='en')
    theme = Column(String, default='system')  # 'light', 'dark', 'system'

    # Security settings
    biometric_auth_enabled = Column(Boolean, default=False)
    session_timeout = Column(String, default='30 minutes')  # '15 minutes', '30 minutes', '1 hour', '2 hours', 'Never'
    app_lock_required = Column(Boolean, default=False)

    # Data & sync settings
    cloud_sync_enabled = Column(Boolean, default=True)
    auto_backup_enabled = Column(Boolean, default=True)
    last_backup_date = Column(DateTime(timezone=True), nullable=True)

    # FCM token for push notifications
    fcm_token = Column(String, nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships: A user can have many tasks, notes, conversations, inbox items, commands, pronunciations, contacts, lessons, notification preferences, and therapy data
    tasks = relationship("Task", back_populates="owner", foreign_keys="Task.owner_id")
    notes = relationship("Note", back_populates="owner")
    calendar_events = relationship("CalendarEvent", back_populates="owner", foreign_keys="CalendarEvent.owner_id")
    attachments = relationship("Attachment", back_populates="owner")
    sync_statuses = relationship("SyncStatus", back_populates="owner")
    conversations = relationship("Conversation", back_populates="owner")
    inbox_items = relationship("InboxItem", back_populates="owner")
    user_commands = relationship("UserCommand", back_populates="owner")
    pronunciations = relationship("Pronunciation", back_populates="owner")
    contacts = relationship("Contact", back_populates="owner")
    lessons = relationship("Lesson", back_populates="owner")
    notification_preferences = relationship("NotificationPreference", back_populates="owner", uselist=False)
    therapy_sessions = relationship("TherapySession", back_populates="user")
    therapy_progress = relationship("TherapyProgress", back_populates="user")
    notification_history = relationship("NotificationHistory", back_populates="owner")
    user_sessions = relationship("UserSession", back_populates="user")
    notifications = relationship("Notification", back_populates="user")


class UserSession(Base):
    """
    Tracks user app sessions for dynamic greeting system and analytics.

    This model stores each time a user opens the app to enable intelligent
    greeting logic and session-based features.
    """
    __tablename__ = "user_sessions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    session_type = Column(String, nullable=False)  # 'app_open', 'app_resume', etc.
    timestamp = Column(DateTime(timezone=True), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship: This session belongs to one user
    user = relationship("User", back_populates="user_sessions")


class Notification(Base):
    """
    Stores scheduled notifications for users.

    This model handles notification scheduling, delivery tracking, and user interactions
    for the comprehensive notification engine.
    """
    __tablename__ = "notifications"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    title = Column(String, nullable=False)
    body = Column(Text, nullable=False)
    type = Column(String, nullable=False)  # 'therapy_reminder', 'task_notification', etc.
    priority = Column(String, nullable=False)  # 'low', 'normal', 'high'
    scheduled_time = Column(DateTime(timezone=True), nullable=False)
    navigation_route = Column(String, nullable=True)
    navigation_data = Column(JSON, nullable=True)

    # Delivery and interaction tracking
    is_delivered = Column(Boolean, default=False)
    is_read = Column(Boolean, default=False)
    is_interacted = Column(Boolean, default=False)
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    read_at = Column(DateTime(timezone=True), nullable=True)
    interacted_at = Column(DateTime(timezone=True), nullable=True)

    # Queue management and retry logic
    delivery_attempts = Column(Integer, default=0)
    last_attempt_at = Column(DateTime(timezone=True), nullable=True)
    failure_reason = Column(String, nullable=True)
    is_failed = Column(Boolean, default=False)
    retry_after = Column(DateTime(timezone=True), nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship: This notification belongs to one user
    user = relationship("User", back_populates="notifications")


class Task(Base):
    __tablename__ = "tasks"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    content = Column(String, nullable=False)
    is_completed = Column(Boolean, default=False)

    # New productivity fields
    due_date = Column(DateTime(timezone=True), nullable=True)
    priority = Column(Integer, default=0)  # 0=low, 1=medium, 2=high, 3=urgent
    tags = Column(JSON, default=list)  # Array of strings for categorization
    is_recurring = Column(Boolean, default=False)

    # Subtask hierarchy
    parent_task_id = Column(String, ForeignKey("tasks.id"), nullable=True)

    # Time tracking
    estimated_duration_minutes = Column(Integer, nullable=True)
    actual_duration_minutes = Column(Integer, nullable=True)
    time_tracking_started_at = Column(DateTime(timezone=True), nullable=True)

    # Dependencies and templates
    depends_on_task_ids = Column(JSON, default=list)  # Array of task IDs
    is_template = Column(Boolean, default=False)
    template_data = Column(JSON, nullable=True)

    # Assignment and collaboration
    assigned_to_user_id = Column(String, ForeignKey("users.id"), nullable=True)
    assigned_by_user_id = Column(String, ForeignKey("users.id"), nullable=True)

    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    owner = relationship("User", back_populates="tasks", foreign_keys=[owner_id])
    assigned_to = relationship("User", foreign_keys=[assigned_to_user_id])
    assigned_by = relationship("User", foreign_keys=[assigned_by_user_id])

    # Self-referential relationship for subtasks
    parent_task = relationship("Task", remote_side=[id], backref="subtasks")
    attachments = relationship("Attachment", back_populates="task", cascade="all, delete-orphan")

class Note(Base):
    __tablename__ = "notes"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String, index=True)
    content = Column(Text, nullable=False)

    # Enhanced content support
    content_type = Column(String, default="plain")  # "plain", "markdown", "rich_text"
    content_data = Column(JSON, nullable=True)  # Rich text formatting data

    # New organizational field
    tags = Column(JSON, default=list)  # Array of strings for categorization
    category = Column(String, nullable=True)  # Structured category

    # Template and sharing
    is_template = Column(Boolean, default=False)
    is_shared = Column(Boolean, default=False)
    shared_with = Column(JSON, default=list)  # Array of user IDs

    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship: This note belongs to one user
    owner = relationship("User", back_populates="notes")
    attachments = relationship("Attachment", back_populates="note", cascade="all, delete-orphan")

class CalendarEvent(Base):
    __tablename__ = "calendar_events"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Event timing
    start_datetime = Column(DateTime(timezone=True), nullable=False)
    end_datetime = Column(DateTime(timezone=True), nullable=False)
    is_all_day = Column(Boolean, default=False)
    timezone = Column(String, default="UTC")

    # Recurrence
    is_recurring = Column(Boolean, default=False)
    recurrence_rule = Column(String, nullable=True)  # RRULE format
    recurrence_end_date = Column(DateTime(timezone=True), nullable=True)

    # Organization
    category = Column(String, nullable=True)
    tags = Column(JSON, default=list)
    color = Column(String, nullable=True)  # Hex color code

    # Location and details
    location = Column(String, nullable=True)
    location_data = Column(JSON, nullable=True)  # Coordinates, address details

    # Reminders and notifications
    reminder_minutes_before = Column(JSON, default=list)  # Array of minutes before event

    # External integration
    external_event_id = Column(String, nullable=True)  # For syncing with external calendars
    external_calendar_id = Column(String, nullable=True)
    sync_status = Column(String, default="local")  # "local", "synced", "conflict"

    # Collaboration
    attendees = Column(JSON, default=list)  # Array of attendee objects
    created_by_user_id = Column(String, ForeignKey("users.id"), nullable=True)

    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    owner = relationship("User", back_populates="calendar_events", foreign_keys=[owner_id])
    created_by = relationship("User", foreign_keys=[created_by_user_id])
    attachments = relationship("Attachment", back_populates="calendar_event", cascade="all, delete-orphan")

class Attachment(Base):
    __tablename__ = "attachments"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    filename = Column(String, nullable=False)
    original_filename = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String, nullable=False)

    # Storage details
    storage_provider = Column(String, default="cloudinary")  # "cloudinary", "local", "s3"
    storage_url = Column(String, nullable=False)
    storage_public_id = Column(String, nullable=True)  # For Cloudinary

    # Relationships - an attachment can belong to note, task, or calendar event
    note_id = Column(String, ForeignKey("notes.id"), nullable=True)
    task_id = Column(String, ForeignKey("tasks.id"), nullable=True)
    calendar_event_id = Column(String, ForeignKey("calendar_events.id"), nullable=True)

    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    owner = relationship("User", back_populates="attachments")
    note = relationship("Note", back_populates="attachments")
    task = relationship("Task", back_populates="attachments")
    calendar_event = relationship("CalendarEvent", back_populates="attachments")

class SyncStatus(Base):
    __tablename__ = "sync_status"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # What is being synced
    entity_type = Column(String, nullable=False)  # "note", "task", "calendar_event"
    entity_id = Column(String, nullable=False)

    # Sync details
    device_id = Column(String, nullable=False)
    last_sync_at = Column(DateTime(timezone=True), nullable=False)
    sync_version = Column(Integer, default=1)

    # Conflict resolution
    has_conflicts = Column(Boolean, default=False)
    conflict_data = Column(JSON, nullable=True)
    resolved_at = Column(DateTime(timezone=True), nullable=True)

    # Status tracking
    sync_status = Column(String, default="pending")  # "pending", "synced", "conflict", "failed"
    error_message = Column(String, nullable=True)

    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    owner = relationship("User", back_populates="sync_statuses")

class Conversation(Base):
    __tablename__ = "conversations"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    summary = Column(Text)  # Optional summary of the conversation

    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    owner = relationship("User", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    action_logs = relationship("ActionLog", back_populates="conversation", cascade="all, delete-orphan")
    therapy_sessions = relationship("TherapySession", back_populates="conversation", cascade="all, delete-orphan")

class Message(Base):
    __tablename__ = "messages"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    role = Column(String, nullable=False)  # "human" or "ai"
    content = Column(Text, nullable=False)

    # Vector embedding for semantic search (1024 dimensions for Voyage AI voyage-3-large)
    embedding = mapped_column(Vector(1024))

    conversation_id = Column(String, ForeignKey("conversations.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship
    conversation = relationship("Conversation", back_populates="messages")

class ActionLog(Base):
    __tablename__ = "action_logs"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    tool_name = Column(String, nullable=False)
    input_params = Column(JSON)  # Store tool input parameters as JSON
    output_summary = Column(Text)  # Summary of tool output

    conversation_id = Column(String, ForeignKey("conversations.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship
    conversation = relationship("Conversation", back_populates="action_logs")

class InboxItem(Base):
    __tablename__ = "inbox_items"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    original_url = Column(Text, nullable=False)
    status = Column(String, default="pending")  # pending, processing, complete, failed
    clean_content = Column(Text, nullable=True)
    summary = Column(Text, nullable=True)

    # Enhanced Smart Capture fields
    title = Column(String(500), nullable=True)  # Extracted title
    author = Column(String(200), nullable=True)  # Content author
    publish_date = Column(DateTime(timezone=True), nullable=True)  # Original publish date
    word_count = Column(Integer, nullable=True)  # Content word count
    reading_time = Column(Integer, nullable=True)  # Estimated reading time in minutes
    thumbnail_url = Column(Text, nullable=True)  # Content thumbnail/preview image

    # Categorization and tagging
    tags = Column(ARRAY(String), nullable=True, default=list)  # User-defined tags
    category = Column(String(50), nullable=True)  # AI-suggested category
    content_type = Column(String(50), nullable=True)  # article, video, document, etc.

    # Processing metadata
    item_metadata = Column(JSON, nullable=True)  # Additional extracted metadata
    processing_error = Column(Text, nullable=True)  # Error details if processing failed
    retry_count = Column(Integer, default=0)  # Number of processing retries

    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship: This inbox item belongs to one user
    owner = relationship("User", back_populates="inbox_items")

class UserCommand(Base):
    __tablename__ = "user_commands"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    trigger_phrase = Column(String, nullable=False, index=True)  # Indexed for fast lookup
    action_to_perform = Column(String, nullable=False)  # e.g., "enter_therapy_mode", "start_productivity_session"

    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship: This command belongs to one user
    owner = relationship("User", back_populates="user_commands")

class Pronunciation(Base):
    __tablename__ = "pronunciations"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    word_to_replace = Column(String, nullable=False, index=True)  # Indexed for fast lookup during TTS
    phonetic_pronunciation = Column(String, nullable=False)  # SSML or phonetic representation

    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship: This pronunciation belongs to one user
    owner = relationship("User", back_populates="pronunciations")

class Contact(Base):
    __tablename__ = "contacts"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False, index=True)  # Contact's name
    phone = Column(String, nullable=True)  # Phone number (optional, E.164 format)
    email = Column(String, nullable=True)  # Email address (optional)
    location = Column(String, nullable=True)  # Meeting location/context (optional)
    met_at = Column(String, nullable=True)  # Where/how they met (optional)
    social_media = Column(JSON, default=dict)  # Social media handles as JSON object
    memory_prompt = Column(Text, nullable=True)  # AI memory prompt about this person

    # Image fields for Cloudinary integration
    image_path = Column(String, nullable=True)  # Cloudinary URL
    image_public_id = Column(String, nullable=True)  # Cloudinary public ID

    # Device sync fields
    device_sync_status = Column(String, default='disabled')  # disabled, pending, synced, failed
    device_contact_id = Column(String, nullable=True)  # Device-specific contact ID
    synced_at = Column(DateTime(timezone=True), nullable=True)  # Last successful sync

    # Soft delete
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship: This contact belongs to one user
    owner = relationship("User", back_populates="contacts")


class SocialPlatform(Base):
    __tablename__ = "social_platforms"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False, unique=True, index=True)  # Internal identifier (e.g., 'twitter')
    display_name = Column(String, nullable=False)  # User-facing name (e.g., 'Twitter')
    url_pattern = Column(String, nullable=False)  # URL template with {username} placeholder
    app_scheme = Column(String, nullable=True)  # Deep link scheme for mobile apps
    username_validation = Column(String, nullable=True)  # Regex pattern for username validation
    username_placeholder = Column(String, nullable=True)  # UI placeholder text
    color = Column(String, nullable=True)  # Hex color code for UI
    icon_url = Column(String, nullable=True)  # Platform icon URL
    is_active = Column(Boolean, default=True)  # Whether platform is enabled

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class Lesson(Base):
    __tablename__ = "lessons"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    scenario = Column(Text, nullable=False)  # The learning scenario or situation
    choice = Column(Text, nullable=False)  # The choice or decision made
    lesson = Column(Text, nullable=False)  # The lesson learned from this experience

    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship: This lesson belongs to one user
    owner = relationship("User", back_populates="lessons")

class NotificationPreference(Base):
    __tablename__ = "notification_preferences"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    # Notification type toggles (matching frontend requirements)
    therapy_reminders = Column(Boolean, default=True)
    task_notifications = Column(Boolean, default=True)
    daily_check_ins = Column(Boolean, default=False)
    weekly_reports = Column(Boolean, default=True)
    system_updates = Column(Boolean, default=False)
    contextual_ai = Column(Boolean, default=True)

    # Quiet hours settings
    quiet_hours_enabled = Column(Boolean, default=False)
    quiet_hours_start_hour = Column(Integer, default=22)
    quiet_hours_start_minute = Column(Integer, default=0)
    quiet_hours_end_hour = Column(Integer, default=8)
    quiet_hours_end_minute = Column(Integer, default=0)

    # Notification preferences
    notification_frequency = Column(String, default='Normal')  # 'Low', 'Normal', 'High'
    sound_enabled = Column(Boolean, default=True)
    vibration_enabled = Column(Boolean, default=True)
    badge_count_enabled = Column(Boolean, default=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship: This preference belongs to one user
    owner = relationship("User", back_populates="notification_preferences")

class NotificationHistory(Base):
    __tablename__ = "notification_history"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    owner_id = Column(String, ForeignKey("users.id"), nullable=False)

    notification_type = Column(String, nullable=False, index=True)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)

    # Simple analytics
    sent_at = Column(DateTime(timezone=True), server_default=func.now())
    clicked_at = Column(DateTime(timezone=True), nullable=True)
    dismissed_at = Column(DateTime(timezone=True), nullable=True)

    relevance_score = Column(Float, nullable=False)

    # Relationship: This notification belongs to one user
    owner = relationship("User", back_populates="notification_history")


class TherapyKnowledgeChunk(Base):
    """
    Stores therapy knowledge base content with vector embeddings for semantic retrieval.

    This model stores chunked therapy content from books, articles, and therapeutic frameworks
    with their corresponding vector embeddings for efficient semantic search during therapy sessions.
    """
    __tablename__ = "therapy_knowledge_chunks"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    content = Column(Text, nullable=False)

    # Vector embedding for semantic search (1024 dimensions for Voyage AI voyage-3-large)
    embedding = mapped_column(Vector(1024))

    # Metadata for better retrieval and categorization
    chapter = Column(String, nullable=True)
    section = Column(String, nullable=True)
    therapy_technique = Column(String, nullable=True)  # CBT, ACT, DBT, etc.
    emotional_themes = Column(JSON, nullable=True)  # Array of emotional themes (anxiety, depression, etc.)
    keywords = Column(JSON, nullable=True)  # Array of relevant keywords for filtering

    # Chunk metadata for reconstruction and context
    chunk_index = Column(Integer, nullable=False)
    total_chunks = Column(Integer, nullable=True)
    source_document = Column(String, nullable=True)  # Source book/article identifier

    # Usage analytics for improving retrieval
    retrieval_count = Column(Integer, default=0)
    effectiveness_score = Column(Float, default=0.0)  # Based on user engagement

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class TherapySession(Base):
    """
    Tracks therapy sessions across voice and chat modalities with unified context.

    This model maintains session state, emotional journey, and therapeutic progress
    across seamless voice-chat handoffs during therapy interactions.
    """
    __tablename__ = "therapy_sessions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(String, ForeignKey("conversations.id"), nullable=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)

    # Session configuration and state
    session_mode = Column(String, default="chat")  # "chat", "voice", "mixed"
    current_modality = Column(String, default="chat")  # Current active modality
    session_focus = Column(String, nullable=True)  # anxiety, depression, stress, etc.
    session_goals = Column(JSON, nullable=True)  # Array of session-specific goals

    # Emotional journey tracking
    initial_emotional_state = Column(JSON, nullable=True)  # Detected emotional state at start
    current_emotional_state = Column(JSON, nullable=True)  # Current emotional state
    emotional_progression = Column(JSON, nullable=True)  # Array of emotional state changes

    # Therapeutic content and interventions
    retrieved_techniques = Column(JSON, nullable=True)  # Therapy techniques used in session
    practice_tasks_assigned = Column(JSON, nullable=True)  # Tasks assigned during session
    key_insights = Column(JSON, nullable=True)  # Important insights discovered

    # Session metadata
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    last_activity_at = Column(DateTime(timezone=True), onupdate=func.now())
    ended_at = Column(DateTime(timezone=True), nullable=True)
    session_duration_minutes = Column(Integer, nullable=True)

    # Modality usage tracking
    voice_duration_minutes = Column(Integer, default=0)
    chat_message_count = Column(Integer, default=0)
    modality_switches = Column(Integer, default=0)  # Number of voice-chat switches

    # Relationships
    conversation = relationship("Conversation", back_populates="therapy_sessions")
    user = relationship("User", back_populates="therapy_sessions")
    progress_entries = relationship("TherapyProgress", back_populates="session")


class TherapyProgress(Base):
    """
    Tracks user's therapeutic progress, insights, and intervention effectiveness.

    This model stores progress data, mood tracking, practice task completion,
    and personalized therapy effectiveness metrics for continuous improvement.
    """
    __tablename__ = "therapy_progress"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    session_id = Column(String, ForeignKey("therapy_sessions.id"), nullable=True)

    # Progress tracking
    progress_type = Column(String, nullable=False)  # "mood_entry", "practice_task", "insight", "milestone"
    content = Column(Text, nullable=False)  # Description of progress entry

    # Mood and emotional tracking
    mood_score = Column(Integer, nullable=True)  # 1-10 scale
    anxiety_level = Column(Integer, nullable=True)  # 1-10 scale
    emotional_tags = Column(JSON, nullable=True)  # Array of emotional descriptors

    # Practice task tracking
    task_name = Column(String, nullable=True)
    task_completed = Column(Boolean, default=False)
    task_effectiveness_rating = Column(Integer, nullable=True)  # 1-5 scale
    task_notes = Column(Text, nullable=True)

    # Intervention effectiveness
    intervention_used = Column(String, nullable=True)  # Specific therapy technique used
    intervention_effectiveness = Column(Float, nullable=True)  # Calculated effectiveness score
    user_feedback_rating = Column(Integer, nullable=True)  # User's rating of helpfulness

    # Contextual metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    context_tags = Column(JSON, nullable=True)  # Additional context (time of day, triggers, etc.)

    # Relationships
    user = relationship("User", back_populates="therapy_progress")
    session = relationship("TherapySession", back_populates="progress_entries")