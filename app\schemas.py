from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

# User schemas
class UserBase(BaseModel):
    email: EmailStr

class UserCreate(UserBase):
    pass

class UserInDB(UserBase):
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class UserResponse(UserInDB):
    display_name: Optional[str] = None
    profile_picture_url: Optional[str] = None
    darvis_name: Optional[str] = None
    time_zone: Optional[str] = None
    language: Optional[str] = None
    theme: Optional[str] = None
    biometric_auth_enabled: Optional[bool] = None
    session_timeout: Optional[str] = None
    app_lock_required: Optional[bool] = None
    cloud_sync_enabled: Optional[bool] = None
    auto_backup_enabled: Optional[bool] = None
    last_activity_date: Optional[datetime] = None

    class Config:
        from_attributes = True

# Task schemas
class TaskBase(BaseModel):
    content: str = Field(..., min_length=1, max_length=1000, description="Task content")
    due_date: Optional[datetime] = Field(None, description="Task due date")
    priority: Optional[int] = Field(0, ge=0, le=3, description="Task priority (0=low, 1=medium, 2=high, 3=urgent)")
    tags: Optional[List[str]] = Field(default_factory=list, description="Task tags for categorization")
    is_recurring: Optional[bool] = Field(False, description="Whether task is recurring")

    # Enhanced fields
    parent_task_id: Optional[str] = Field(None, description="Parent task ID for subtasks")
    estimated_duration_minutes: Optional[int] = Field(None, ge=1, description="Estimated duration in minutes")
    depends_on_task_ids: Optional[List[str]] = Field(default_factory=list, description="Task dependencies")
    is_template: Optional[bool] = Field(False, description="Whether task is a template")
    template_data: Optional[Dict[str, Any]] = Field(None, description="Template configuration data")
    assigned_to_user_id: Optional[str] = Field(None, description="User ID task is assigned to")

class TaskCreate(TaskBase):
    class Config:
        json_schema_extra = {
            "example": {
                "content": "Complete the quarterly financial report",
                "due_date": "2025-02-15T17:00:00Z",
                "priority": 2,
                "tags": ["work", "finance", "quarterly"],
                "is_recurring": False
            }
        }

class TaskUpdate(BaseModel):
    content: Optional[str] = Field(None, min_length=1, max_length=1000, description="Task content")
    is_completed: Optional[bool] = Field(None, description="Task completion status")
    due_date: Optional[datetime] = Field(None, description="Task due date")
    priority: Optional[int] = Field(None, ge=0, le=3, description="Task priority (0=low, 1=medium, 2=high, 3=urgent)")
    tags: Optional[List[str]] = Field(None, description="Task tags for categorization")
    is_recurring: Optional[bool] = Field(None, description="Whether task is recurring")

    # Enhanced fields
    parent_task_id: Optional[str] = Field(None, description="Parent task ID for subtasks")
    estimated_duration_minutes: Optional[int] = Field(None, ge=1, description="Estimated duration in minutes")
    actual_duration_minutes: Optional[int] = Field(None, ge=1, description="Actual duration in minutes")
    depends_on_task_ids: Optional[List[str]] = Field(None, description="Task dependencies")
    assigned_to_user_id: Optional[str] = Field(None, description="User ID task is assigned to")

    class Config:
        json_schema_extra = {
            "example": {
                "content": "Complete the quarterly financial report - updated with new requirements",
                "is_completed": True,
                "priority": 3,
                "tags": ["work", "finance", "quarterly", "urgent"]
            }
        }

class TaskInDB(TaskBase):
    id: str
    is_completed: bool
    actual_duration_minutes: Optional[int] = None
    time_tracking_started_at: Optional[datetime] = None
    assigned_by_user_id: Optional[str] = None
    owner_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Note schemas
class NoteBase(BaseModel):
    title: Optional[str] = Field(None, max_length=200, description="Note title")
    content: str = Field(..., min_length=1, max_length=50000, description="Note content")
    content_type: Optional[str] = Field("plain", description="Content type: plain, markdown, rich_text")
    content_data: Optional[Dict[str, Any]] = Field(None, description="Rich text formatting data")
    tags: Optional[List[str]] = Field(default_factory=list, description="Note tags for categorization")
    category: Optional[str] = Field(None, max_length=100, description="Note category")
    is_template: Optional[bool] = Field(False, description="Whether note is a template")
    is_shared: Optional[bool] = Field(False, description="Whether note is shared")
    shared_with: Optional[List[str]] = Field(default_factory=list, description="User IDs note is shared with")

class NoteCreate(NoteBase):
    class Config:
        json_schema_extra = {
            "example": {
                "title": "Meeting Notes - Product Strategy",
                "content": "Discussed Q1 roadmap priorities:\n1. User authentication improvements\n2. Mobile app performance optimization\n3. New dashboard features\n\nAction items:\n- Schedule follow-up with engineering team\n- Review budget allocation for Q1",
                "tags": ["meeting", "strategy", "q1", "product"]
            }
        }

class NoteUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=200, description="Note title")
    content: Optional[str] = Field(None, min_length=1, max_length=50000, description="Note content")
    content_type: Optional[str] = Field(None, description="Content type: plain, markdown, rich_text")
    content_data: Optional[Dict[str, Any]] = Field(None, description="Rich text formatting data")
    tags: Optional[List[str]] = Field(None, description="Note tags for categorization")
    category: Optional[str] = Field(None, max_length=100, description="Note category")
    is_shared: Optional[bool] = Field(None, description="Whether note is shared")
    shared_with: Optional[List[str]] = Field(None, description="User IDs note is shared with")

    class Config:
        json_schema_extra = {
            "example": {
                "title": "Meeting Notes - Product Strategy (Updated)",
                "content": "Updated meeting notes with additional insights from the engineering team...",
                "tags": ["meeting", "strategy", "q1", "product", "engineering"]
            }
        }

class NoteInDB(NoteBase):
    id: str
    owner_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Response schemas
class TaskResponse(TaskInDB):
    pass

# Calendar Event schemas
class CalendarEventBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200, description="Event title")
    description: Optional[str] = Field(None, max_length=2000, description="Event description")
    start_datetime: datetime = Field(..., description="Event start date and time")
    end_datetime: datetime = Field(..., description="Event end date and time")
    is_all_day: Optional[bool] = Field(False, description="Whether event is all day")
    timezone: Optional[str] = Field("UTC", description="Event timezone")

    # Recurrence
    is_recurring: Optional[bool] = Field(False, description="Whether event is recurring")
    recurrence_rule: Optional[str] = Field(None, description="Recurrence rule in RRULE format")
    recurrence_end_date: Optional[datetime] = Field(None, description="When recurrence ends")

    # Organization
    category: Optional[str] = Field(None, max_length=100, description="Event category")
    tags: Optional[List[str]] = Field(default_factory=list, description="Event tags")
    color: Optional[str] = Field(None, description="Event color (hex code)")

    # Location
    location: Optional[str] = Field(None, max_length=500, description="Event location")
    location_data: Optional[Dict[str, Any]] = Field(None, description="Location details (coordinates, etc.)")

    # Reminders
    reminder_minutes_before: Optional[List[int]] = Field(default_factory=list, description="Reminder times in minutes before event")

    # Collaboration
    attendees: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="Event attendees")

class CalendarEventCreate(CalendarEventBase):
    class Config:
        json_schema_extra = {
            "example": {
                "title": "Team Meeting",
                "description": "Weekly team sync meeting",
                "start_datetime": "2025-02-15T10:00:00Z",
                "end_datetime": "2025-02-15T11:00:00Z",
                "category": "work",
                "tags": ["meeting", "team"],
                "location": "Conference Room A",
                "reminder_minutes_before": [15, 5]
            }
        }

class CalendarEventUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="Event title")
    description: Optional[str] = Field(None, max_length=2000, description="Event description")
    start_datetime: Optional[datetime] = Field(None, description="Event start date and time")
    end_datetime: Optional[datetime] = Field(None, description="Event end date and time")
    is_all_day: Optional[bool] = Field(None, description="Whether event is all day")
    timezone: Optional[str] = Field(None, description="Event timezone")
    is_recurring: Optional[bool] = Field(None, description="Whether event is recurring")
    recurrence_rule: Optional[str] = Field(None, description="Recurrence rule in RRULE format")
    recurrence_end_date: Optional[datetime] = Field(None, description="When recurrence ends")
    category: Optional[str] = Field(None, max_length=100, description="Event category")
    tags: Optional[List[str]] = Field(None, description="Event tags")
    color: Optional[str] = Field(None, description="Event color (hex code)")
    location: Optional[str] = Field(None, max_length=500, description="Event location")
    location_data: Optional[Dict[str, Any]] = Field(None, description="Location details")
    reminder_minutes_before: Optional[List[int]] = Field(None, description="Reminder times")
    attendees: Optional[List[Dict[str, Any]]] = Field(None, description="Event attendees")

class CalendarEventInDB(CalendarEventBase):
    id: str
    external_event_id: Optional[str] = None
    external_calendar_id: Optional[str] = None
    sync_status: str = "local"
    created_by_user_id: Optional[str] = None
    owner_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class NoteResponse(NoteInDB):
    pass

class CalendarEventResponse(CalendarEventInDB):
    pass

class UserResponse(UserInDB):
    display_name: Optional[str] = None
    profile_picture_url: Optional[str] = None
    darvis_name: Optional[str] = None
    time_zone: Optional[str] = None
    language: Optional[str] = None
    theme: Optional[str] = None
    biometric_auth_enabled: Optional[bool] = None
    session_timeout: Optional[str] = None
    app_lock_required: Optional[bool] = None
    cloud_sync_enabled: Optional[bool] = None
    auto_backup_enabled: Optional[bool] = None
    last_activity_date: Optional[datetime] = None

    class Config:
        from_attributes = True

# Attachment schemas
class AttachmentBase(BaseModel):
    filename: str = Field(..., min_length=1, max_length=255, description="File name")
    original_filename: str = Field(..., min_length=1, max_length=255, description="Original file name")
    file_size: int = Field(..., ge=1, description="File size in bytes")
    mime_type: str = Field(..., description="MIME type of the file")

class AttachmentCreate(AttachmentBase):
    storage_url: str = Field(..., description="Storage URL for the file")
    storage_public_id: Optional[str] = Field(None, description="Storage provider public ID")
    note_id: Optional[str] = Field(None, description="Associated note ID")
    task_id: Optional[str] = Field(None, description="Associated task ID")
    calendar_event_id: Optional[str] = Field(None, description="Associated calendar event ID")

class AttachmentInDB(AttachmentBase):
    id: str
    storage_provider: str = "cloudinary"
    storage_url: str
    storage_public_id: Optional[str] = None
    note_id: Optional[str] = None
    task_id: Optional[str] = None
    calendar_event_id: Optional[str] = None
    owner_id: str
    created_at: datetime

    class Config:
        from_attributes = True

class AttachmentResponse(AttachmentInDB):
    pass

# Sync schemas
class SyncStatusBase(BaseModel):
    entity_type: str = Field(..., description="Type of entity being synced")
    entity_id: str = Field(..., description="ID of the entity being synced")
    device_id: str = Field(..., description="Device identifier")

class SyncStatusCreate(SyncStatusBase):
    sync_version: Optional[int] = Field(1, description="Sync version number")

class SyncStatusUpdate(BaseModel):
    sync_status: Optional[str] = Field(None, description="Sync status")
    has_conflicts: Optional[bool] = Field(None, description="Whether there are conflicts")
    conflict_data: Optional[Dict[str, Any]] = Field(None, description="Conflict resolution data")
    error_message: Optional[str] = Field(None, description="Error message if sync failed")

class SyncStatusInDB(SyncStatusBase):
    id: str
    last_sync_at: datetime
    sync_version: int = 1
    has_conflicts: bool = False
    conflict_data: Optional[Dict[str, Any]] = None
    resolved_at: Optional[datetime] = None
    sync_status: str = "pending"
    error_message: Optional[str] = None
    owner_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class SyncStatusResponse(SyncStatusInDB):
    pass

# List response schemas
class TaskListResponse(BaseModel):
    tasks: list[TaskResponse]
    total: int

class NoteListResponse(BaseModel):
    notes: list[NoteResponse]
    total: int

class CalendarEventListResponse(BaseModel):
    events: list[CalendarEventResponse]
    total: int

class AttachmentListResponse(BaseModel):
    attachments: list[AttachmentResponse]
    total: int

# Time tracking schemas
class TimeTrackingStart(BaseModel):
    task_id: str = Field(..., description="Task ID to start tracking time for")

class TimeTrackingStop(BaseModel):
    task_id: str = Field(..., description="Task ID to stop tracking time for")

class TimeTrackingResponse(BaseModel):
    task_id: str
    started_at: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    is_tracking: bool = False

# Subtask management schemas
class SubtaskCreate(BaseModel):
    parent_task_id: str = Field(..., description="Parent task ID")
    content: str = Field(..., min_length=1, max_length=1000, description="Subtask content")
    priority: Optional[int] = Field(0, ge=0, le=3, description="Subtask priority")

class SubtaskListResponse(BaseModel):
    subtasks: list[TaskResponse]
    total: int

# Template schemas
class TemplateCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=200, description="Template name")
    description: Optional[str] = Field(None, max_length=1000, description="Template description")
    template_type: str = Field(..., description="Type: note, task, calendar_event")
    template_data: Dict[str, Any] = Field(..., description="Template configuration")

class TemplateResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    template_type: str
    template_data: Dict[str, Any]
    owner_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# InboxItem schemas
class InboxItemCreate(BaseModel):
    original_url: str = Field(..., min_length=1, max_length=2000, description="URL to capture and process")
    tags: Optional[List[str]] = Field(default=None, description="User-defined tags for categorization")
    category: Optional[str] = Field(default=None, max_length=50, description="Content category")

    class Config:
        json_schema_extra = {
            "example": {
                "original_url": "https://techcrunch.com/2025/01/23/ai-breakthrough-article",
                "tags": ["tech", "ai", "news"],
                "category": "article"
            }
        }

class InboxItemResponse(BaseModel):
    id: str
    original_url: str
    status: str
    clean_content: Optional[str] = None
    summary: Optional[str] = None

    # Enhanced Smart Capture fields
    title: Optional[str] = None
    author: Optional[str] = None
    publish_date: Optional[datetime] = None
    word_count: Optional[int] = None
    reading_time: Optional[int] = None
    thumbnail_url: Optional[str] = None

    # Categorization and tagging
    tags: Optional[List[str]] = None
    category: Optional[str] = None
    content_type: Optional[str] = None

    # Processing metadata
    item_metadata: Optional[Dict[str, Any]] = None
    processing_error: Optional[str] = None
    retry_count: Optional[int] = None

    owner_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class InboxItemListResponse(BaseModel):
    inbox_items: list[InboxItemResponse]
    total: int

# Enhanced Smart Capture schemas
class InboxItemUpdate(BaseModel):
    tags: Optional[List[str]] = None
    category: Optional[str] = Field(default=None, max_length=50)
    title: Optional[str] = Field(default=None, max_length=500)

class InboxItemSearchRequest(BaseModel):
    query: Optional[str] = Field(default=None, description="Search query across title and content")
    tags: Optional[List[str]] = Field(default=None, description="Filter by tags")
    category: Optional[str] = Field(default=None, description="Filter by category")
    content_type: Optional[str] = Field(default=None, description="Filter by content type")
    status: Optional[str] = Field(default=None, description="Filter by processing status")
    date_from: Optional[datetime] = Field(default=None, description="Filter from date")
    date_to: Optional[datetime] = Field(default=None, description="Filter to date")
    sort_by: Optional[str] = Field(default="created_at", description="Sort field")
    sort_order: Optional[str] = Field(default="desc", description="Sort order (asc/desc)")
    page: Optional[int] = Field(default=1, ge=1, description="Page number")
    limit: Optional[int] = Field(default=20, ge=1, le=100, description="Items per page")

class InboxItemSearchResponse(BaseModel):
    items: List[InboxItemResponse]
    total: int
    page: int
    limit: int
    available_tags: List[str]
    available_categories: List[str]

class InboxItemBulkDeleteRequest(BaseModel):
    item_ids: List[str] = Field(..., min_items=1, max_items=100, description="List of item IDs to delete")

class InboxItemBulkCategorizeRequest(BaseModel):
    item_ids: List[str] = Field(..., min_items=1, max_items=100, description="List of item IDs to categorize")
    category: str = Field(..., max_length=50, description="Category to assign")

class InboxItemBulkTagRequest(BaseModel):
    item_ids: List[str] = Field(..., min_items=1, max_items=100, description="List of item IDs to tag")
    tags: List[str] = Field(..., min_items=1, description="Tags to add")
    operation: str = Field(..., description="Operation: 'add' or 'remove'")

class InboxItemBulkRetryRequest(BaseModel):
    item_ids: List[str] = Field(..., min_items=1, max_items=100, description="List of item IDs to retry processing")

class InboxItemBulkResponse(BaseModel):
    success: bool
    processed_count: int
    failed_count: int
    message: str
    failed_items: Optional[List[str]] = None

class InboxItemAddToNotesRequest(BaseModel):
    note_title: Optional[str] = Field(default=None, max_length=200, description="Custom note title")
    note_tags: Optional[List[str]] = Field(default=None, description="Tags for the created note")

class InboxItemAddToNotesResponse(BaseModel):
    success: bool
    note_id: str
    message: str

class InboxItemChatContextResponse(BaseModel):
    success: bool
    context_id: str
    message: str

# UserCommand schemas
class UserCommandBase(BaseModel):
    trigger_phrase: str = Field(..., min_length=1, max_length=200, description="Phrase that triggers the command")
    action_to_perform: str = Field(..., min_length=1, max_length=100, description="Action to execute (e.g., 'enter_therapy_mode')")

class UserCommandCreate(UserCommandBase):
    class Config:
        json_schema_extra = {
            "example": {
                "trigger_phrase": "enter therapy mode",
                "action_to_perform": "enter_therapy_mode"
            }
        }

class UserCommandUpdate(BaseModel):
    trigger_phrase: Optional[str] = Field(None, min_length=1, max_length=200, description="Phrase that triggers the command")
    action_to_perform: Optional[str] = Field(None, min_length=1, max_length=100, description="Action to execute")

    class Config:
        json_schema_extra = {
            "example": {
                "trigger_phrase": "activate focus mode",
                "action_to_perform": "enter_focus_mode"
            }
        }

class UserCommandResponse(UserCommandBase):
    id: str
    owner_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class UserCommandListResponse(BaseModel):
    commands: list[UserCommandResponse]
    total: int

# Pronunciation schemas
class PronunciationBase(BaseModel):
    word_to_replace: str = Field(..., min_length=1, max_length=100, description="Word to replace in TTS")
    phonetic_pronunciation: str = Field(..., min_length=1, max_length=200, description="Phonetic pronunciation or SSML")

class PronunciationCreate(PronunciationBase):
    class Config:
        json_schema_extra = {
            "example": {
                "word_to_replace": "Darvis",
                "phonetic_pronunciation": "DAR-vis"
            }
        }

class PronunciationUpdate(BaseModel):
    word_to_replace: Optional[str] = Field(None, min_length=1, max_length=100, description="Word to replace in TTS")
    phonetic_pronunciation: Optional[str] = Field(None, min_length=1, max_length=200, description="Phonetic pronunciation or SSML")

    class Config:
        json_schema_extra = {
            "example": {
                "word_to_replace": "Darvis",
                "phonetic_pronunciation": "DAHR-vis"
            }
        }

class PronunciationResponse(PronunciationBase):
    id: str
    owner_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class PronunciationListResponse(BaseModel):
    pronunciations: list[PronunciationResponse]
    total: int

# Contact schemas
class ContactBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200, description="Contact's name")
    phone: Optional[str] = Field(None, max_length=50, description="Phone number (E.164 format preferred)")
    email: Optional[str] = Field(None, max_length=255, description="Email address")
    location: Optional[str] = Field(None, max_length=200, description="Meeting location or context")
    met_at: Optional[str] = Field(None, max_length=500, description="Where/how you met this person")
    social_media: Optional[Dict[str, str]] = Field(default_factory=dict, description="Social media handles")
    memory_prompt: Optional[str] = Field(None, max_length=2000, description="AI memory prompt about this person")
    image_path: Optional[str] = Field(None, description="Contact image URL")
    device_sync_status: Optional[str] = Field(default='disabled', description="Device sync status: disabled, pending, synced, failed")

class ContactCreate(ContactBase):
    class Config:
        json_schema_extra = {
            "example": {
                "name": "Sarah Johnson",
                "phone": "******-0123",
                "email": "<EMAIL>",
                "location": "Coffee Shop Downtown",
                "met_at": "Tech conference in San Francisco, 2024",
                "social_media": {
                    "linkedin": "sarah-johnson-dev",
                    "twitter": "sarahj_codes",
                    "instagram": "sarah.codes"
                },
                "memory_prompt": "Software engineer at Google, passionate about AI ethics. Mentioned working on a new ML framework for healthcare applications.",
                "device_sync_status": "disabled"
            }
        }

class ContactUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Contact's name")
    phone: Optional[str] = Field(None, max_length=50, description="Phone number")
    email: Optional[str] = Field(None, max_length=255, description="Email address")
    location: Optional[str] = Field(None, max_length=200, description="Meeting location or context")
    met_at: Optional[str] = Field(None, max_length=500, description="Where/how you met this person")
    social_media: Optional[Dict[str, str]] = Field(None, description="Social media handles")
    memory_prompt: Optional[str] = Field(None, max_length=2000, description="AI memory prompt about this person")
    image_path: Optional[str] = Field(None, description="Contact image URL")
    device_sync_status: Optional[str] = Field(None, description="Device sync status")

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Sarah Johnson",
                "phone": "******-0123",
                "email": "<EMAIL>",
                "location": "Updated location",
                "memory_prompt": "Software engineer at Google, passionate about AI ethics. Recently promoted to Senior Engineer. Working on healthcare ML framework.",
                "device_sync_status": "synced"
            }
        }

class ContactResponse(ContactBase):
    id: str
    owner_id: str
    image_public_id: Optional[str] = None
    device_contact_id: Optional[str] = None
    synced_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ContactListResponse(BaseModel):
    contacts: list[ContactResponse]
    total: int

# Social Platform schemas
class SocialPlatformResponse(BaseModel):
    name: str
    display_name: str
    url_pattern: str
    app_scheme: Optional[str] = None
    username_validation: Optional[str] = None
    username_placeholder: Optional[str] = None
    color: Optional[str] = None
    icon_url: Optional[str] = None
    is_active: bool = True

    class Config:
        from_attributes = True

class SocialPlatformsResponse(BaseModel):
    platforms: list[SocialPlatformResponse]

# Device sync schemas
class DeviceSyncRequest(BaseModel):
    device_contact_id: Optional[str] = None
    sync_status: str = Field(..., description="Sync status: synced, failed, pending")
    sync_error: Optional[str] = None
    device_info: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Device information")

class DeviceSyncResponse(BaseModel):
    contact_id: str
    sync_status: str
    synced_at: Optional[datetime] = None
    sync_error: Optional[str] = None

class BulkDeviceSyncRequest(BaseModel):
    contact_ids: list[str] = Field(..., description="List of contact IDs to sync")
    device_info: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Device information")

class BulkDeviceSyncResponse(BaseModel):
    results: list[DeviceSyncResponse]
    success_count: int
    failed_count: int

# Export schemas
class ContactExportRequest(BaseModel):
    contact_ids: Optional[list[str]] = Field(None, description="Specific contact IDs to export (if None, exports all)")
    format: str = Field(default="vcf", description="Export format: vcf, csv, json")
    include_images: bool = Field(default=False, description="Include contact images in export")

# Search schemas
class ContactSearchRequest(BaseModel):
    query: str = Field(..., min_length=1, description="Search query")
    fields: Optional[list[str]] = Field(None, description="Fields to search in")
    platform: Optional[str] = Field(None, description="Filter by social platform")
    date_from: Optional[datetime] = Field(None, description="Created after date")
    date_to: Optional[datetime] = Field(None, description="Created before date")
    limit: int = Field(default=20, ge=1, le=100)
    offset: int = Field(default=0, ge=0)

class ContactSearchResponse(BaseModel):
    results: list[ContactResponse]
    total: int
    query: str
    filters: Dict[str, Any]

# Analytics schemas
class ContactAnalyticsResponse(BaseModel):
    total_contacts: int
    contacts_this_month: int
    top_social_platforms: list[Dict[str, Any]]
    top_locations: list[Dict[str, Any]]
    device_sync_stats: Dict[str, int]

# Social verification schemas
class SocialVerificationRequest(BaseModel):
    platform: str
    username: str

class SocialVerificationResponse(BaseModel):
    verified: bool
    profile_exists: bool
    profile_url: str
    profile_info: Optional[Dict[str, Any]] = None

# Lesson schemas
class LessonBase(BaseModel):
    scenario: str = Field(..., min_length=1, max_length=2000, description="The learning scenario or situation")
    choice: str = Field(..., min_length=1, max_length=1000, description="The choice or decision made")
    lesson: str = Field(..., min_length=1, max_length=2000, description="The lesson learned from this experience")

class LessonCreate(LessonBase):
    class Config:
        json_schema_extra = {
            "example": {
                "scenario": "I was leading a team project with a tight deadline. One team member was consistently missing deadlines and not communicating their progress.",
                "choice": "I decided to have a private one-on-one conversation with them to understand the root cause rather than escalating to management immediately.",
                "lesson": "Taking time to understand underlying issues before taking action often leads to better outcomes. The team member was dealing with personal challenges and appreciated the support. We adjusted the workload and the project was completed successfully."
            }
        }

class LessonUpdate(BaseModel):
    scenario: Optional[str] = Field(None, min_length=1, max_length=2000, description="The learning scenario or situation")
    choice: Optional[str] = Field(None, min_length=1, max_length=1000, description="The choice or decision made")
    lesson: Optional[str] = Field(None, min_length=1, max_length=2000, description="The lesson learned from this experience")

    class Config:
        json_schema_extra = {
            "example": {
                "lesson": "Taking time to understand underlying issues before taking action often leads to better outcomes. The team member was dealing with personal challenges and appreciated the support. We adjusted the workload and the project was completed successfully. UPDATE: This approach has worked well in subsequent similar situations."
            }
        }

class LessonResponse(LessonBase):
    id: str
    owner_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class LessonListResponse(BaseModel):
    lessons: list[LessonResponse]
    total: int
