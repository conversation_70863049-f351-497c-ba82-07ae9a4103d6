"""
Social Media Platforms API for Contact Management.

This module provides endpoints for managing social media platform configurations
and validation rules for contact social media integration.
"""

from fastapi import API<PERSON><PERSON>er, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any

from app.db.database import get_db
from app.api.auth import get_current_user
from app.db import crud
from app.schemas import (
    SocialPlatformsResponse,
    SocialVerificationRequest,
    SocialVerificationResponse
)
import logging
import re

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/social-platforms", tags=["Social Platforms"])

@router.get("/", response_model=SocialPlatformsResponse)
async def get_social_platforms(
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """
    Get all supported social media platforms with validation rules.
    
    **Authentication**: Not required (public endpoint)
    
    **Query Parameters**:
    - **active_only**: Return only active platforms (default: true)
    
    **Returns**:
    - **200 OK**: List of social platforms with configuration
    
    **Response includes**:
    - Platform name and display name
    - URL pattern for profile links
    - App scheme for deep linking
    - Username validation regex
    - UI configuration (placeholder, color, icon)
    """
    try:
        platforms = crud.get_social_platforms(db=db, active_only=active_only)
        
        return SocialPlatformsResponse(platforms=platforms)
        
    except Exception as e:
        logger.error(f"Error retrieving social platforms: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve social platforms"
        )

@router.post("/{platform_name}/validate")
async def validate_social_username(
    platform_name: str,
    username: str,
    db: Session = Depends(get_db)
):
    """
    Validate a username for a specific social media platform.
    
    **Authentication**: Not required (public endpoint)
    
    **Path Parameters**:
    - **platform_name**: Name of the social media platform
    
    **Query Parameters**:
    - **username**: Username to validate
    
    **Returns**:
    - **200 OK**: Validation result
    - **404 Not Found**: Platform not found
    - **400 Bad Request**: Invalid username format
    """
    try:
        # Get platform configuration
        platform = crud.get_social_platform_by_name(db=db, name=platform_name.lower())
        
        if not platform:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Social platform '{platform_name}' not found"
            )
        
        if not platform.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Social platform '{platform_name}' is not active"
            )
        
        # Validate username format
        is_valid = True
        error_message = None
        
        if platform.username_validation:
            try:
                # Clean username (remove @ if present for platforms that don't use it in validation)
                clean_username = username.lstrip('@') if username.startswith('@') else username
                
                if not re.match(platform.username_validation, clean_username):
                    is_valid = False
                    error_message = f"Username does not match the required format for {platform.display_name}"
            except re.error:
                logger.error(f"Invalid regex pattern for platform {platform_name}: {platform.username_validation}")
                is_valid = False
                error_message = "Invalid validation pattern configured for this platform"
        
        # Generate profile URL if valid
        profile_url = None
        if is_valid and platform.url_pattern:
            try:
                clean_username = username.lstrip('@') if username.startswith('@') else username
                profile_url = platform.url_pattern.format(username=clean_username)
            except KeyError:
                logger.error(f"Invalid URL pattern for platform {platform_name}: {platform.url_pattern}")
        
        return {
            "platform": platform_name,
            "username": username,
            "is_valid": is_valid,
            "error_message": error_message,
            "profile_url": profile_url,
            "validation_pattern": platform.username_validation,
            "placeholder": platform.username_placeholder
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating username for platform {platform_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate username"
        )

@router.post("/{contact_id}/social-verify", response_model=SocialVerificationResponse)
async def verify_social_profile(
    contact_id: str,
    verification_request: SocialVerificationRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Verify if a social media profile exists (optional feature).
    
    **Authentication Required**: Yes (Firebase ID token)
    
    **Path Parameters**:
    - **contact_id**: ID of the contact to verify social profile for
    
    **Request Body**:
    - **platform**: Social media platform name
    - **username**: Username to verify
    
    **Returns**:
    - **200 OK**: Verification result
    - **404 Not Found**: Contact or platform not found
    - **400 Bad Request**: Invalid request
    - **401 Unauthorized**: Invalid authentication token
    
    **Note**: This is a placeholder implementation. Full social media verification
    would require API integrations with each platform and proper rate limiting.
    """
    try:
        user_id = current_user["uid"]
        
        # Verify contact belongs to user
        contact = crud.get_contact_by_id(db=db, contact_id=contact_id, user_id=user_id)
        if not contact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contact not found"
            )
        
        # Get platform configuration
        platform = crud.get_social_platform_by_name(db=db, name=verification_request.platform.lower())
        if not platform:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Social platform '{verification_request.platform}' not found"
            )
        
        # Generate profile URL
        clean_username = verification_request.username.lstrip('@')
        profile_url = platform.url_pattern.format(username=clean_username)
        
        # Placeholder verification logic
        # In a real implementation, this would make API calls to verify profile existence
        # For now, we'll return a basic response based on username validation
        is_valid_format = True
        if platform.username_validation:
            is_valid_format = bool(re.match(platform.username_validation, clean_username))
        
        return SocialVerificationResponse(
            verified=False,  # Placeholder - would be actual verification result
            profile_exists=is_valid_format,  # Placeholder - assume exists if format is valid
            profile_url=profile_url,
            profile_info={
                "display_name": verification_request.username,
                "verified": False,
                "follower_count": None
            } if is_valid_format else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying social profile for contact {contact_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify social profile"
        )

@router.get("/{platform_name}")
async def get_social_platform(
    platform_name: str,
    db: Session = Depends(get_db)
):
    """
    Get configuration for a specific social media platform.
    
    **Authentication**: Not required (public endpoint)
    
    **Path Parameters**:
    - **platform_name**: Name of the social media platform
    
    **Returns**:
    - **200 OK**: Platform configuration
    - **404 Not Found**: Platform not found
    """
    try:
        platform = crud.get_social_platform_by_name(db=db, name=platform_name.lower())
        
        if not platform:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Social platform '{platform_name}' not found"
            )
        
        return platform
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving platform {platform_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve platform configuration"
        )
