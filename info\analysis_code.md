# LiveKit Integration Analysis (Drix Backend)

Date: 2025-08-23

## Executive Summary
- Current status: Mixed/broken
  - livekit-agents is installed and referenced correctly in the voice agent module.
  - livekit-api appears installed in venv, but app/api integration uses incorrect import/usage patterns.
- Primary issues
  - Incorrect import and construction for RoomService in `app/api/agent_management.py`.
  - Wrong use of `AccessToken.with_ttl` (passing a datetime instead of timedelta).
  - Architectural mismatch: API launches a subprocess worker while passing an agent token/room args that the worker ignores; the worker is built for Agent Dispatch, not token-based RTC join.
  - Global initialization of `LiveKitManager` at import-time can crash app startup if LIVEKIT env vars are missing.
- Recommended immediate action
  1) Replace direct `RoomService` usage with `LiveKitAPI` client and fix token TTL.
  2) Lazy-initialize LiveKit manager inside endpoints or guard initialization.
  3) Decide and align on one pattern: Agent Dispatch (recommended) vs token-based direct join. Adjust API accordingly; short term, return user token and skip agent launch to unblock server.

---

## Detailed Findings

### 1) Current LiveKit Usage Analysis
- Import patterns in project code
  - `app/api/agent_management.py`
    - `from livekit.api import RoomService, AccessToken, VideoGrants, CreateRoomRequest`
  - `darvis-ai-core/livekit_agent.py`
    - `from livekit import agents`
    - `from livekit.agents import AgentSession, Agent, stt, tts`
    - `from livekit.plugins.turn_detector.multilingual import MultilingualModel`
- Import locations and purpose
  - `agent_management.py`: Intended to manage rooms/tokens and launch the voice agent.
  - `darvis-ai-core/livekit_agent.py`: Implements the actual voice agent using LiveKit Agents (worker pattern with entrypoint(JobContext)).
- Version dependencies (observed)
  - requirements.txt: `livekit-agents[turn-detector]~=1.0` (installed venv shows `livekit.agents.__version__ = 1.2.2`).
  - livekit-api not listed in requirements.txt, but present in venv with `livekit.api.__version__ = 1.0.5`.
- Actual implementation vs imports
  - `agent_management.py` currently imports `RoomService` directly from `livekit.api`. In the installed package, `RoomService` is not re-exported by `livekit.api.__init__`, so `from livekit.api import RoomService` will fail. Even if imported from `livekit.api.room_service`, the constructor is used incorrectly (expects an aiohttp session as first arg).
  - `AccessToken`, `VideoGrants`, and `CreateRoomRequest` are correctly available from `livekit.api` and are valid to import.

### 2) LiveKit Integration Assessment
- Functional code
  - `darvis-ai-core/livekit_agent.py` uses LiveKit Agents correctly at a high level (AgentSession, entrypoint(JobContext), turn detector plugin).
  - Custom STT (Groq) and TTS (DeepInfra Kokoro) classes are structured to conform to LiveKit Agents STT/TTS interfaces.
- Placeholder/incomplete integration
  - API layer uses a subprocess to launch the worker and passes `--room` and `--token`, but the worker ignores CLI args and uses `agents.cli.run_app(...)` to register for Agent Dispatch. No Agent Dispatch creation is performed by API.
  - Room creation path uses incorrect client (`RoomService` directly) and wrong instantiation.
- Missing dependencies/usage gaps
  - livekit-api is used but not pinned in requirements.txt; risk of env drift.
  - No Agent Dispatch flow defined (no usage of `AgentDispatchService` to create a dispatch to the room).
- Service configuration
  - `.env`/`.env.example` include: `LIVEKIT_URL`, `LIVEKIT_API_KEY`, `LIVEKIT_API_SECRET`.
  - Worker relies on these env vars to connect/register with Agent Dispatch via `agents.cli.run_app(...)`.

### 3) Voice/Audio Features Current State
- Voice processing
  - Implemented in `darvis-ai-core/livekit_agent.py` via LiveKit Agents with custom STT (Groq Whisper) and TTS (Kokoro on DeepInfra).
  - LLM routed through `DarvisOrchestrator` for conversation logic and memory.
- Audio handling
  - LiveKit Agents framework handles I/O; custom TTS returns WAV bytes; custom STT posts captured audio to Groq.
  - No explicit low-level audio I/O in repo; relies on Agents’ abstractions (AgentSession).
- Real-time communication
  - Architected for LiveKit Agents (Agent Dispatch) but API starts a worker process without arranging dispatch; therefore real-time agent joining is currently non-functional end-to-end.
- Agent integration
  - Agent entrypoint consumes `JobContext` (provided by Agent Dispatch), extracts room metadata (user_id, mode, conversation_id), and starts an AgentSession.

### 4) Import Error Root Cause Analysis
- Specific problem (agent_management.py)
  - `from livekit.api import RoomService` fails because `livekit.api.__init__` does not export `RoomService`.
  - Additionally, constructor usage is wrong: `RoomService(self.livekit_url, self.api_key, self.api_secret)` should be created by `LiveKitAPI`, which manages an aiohttp session and exposes `room: RoomService`.
  - Token generation bug: `token.with_ttl(expires_at)` passes a datetime, but API expects `timedelta`. This will cause runtime errors when calling `to_jwt()`.
- Alternative working patterns in codebase
  - `darvis-ai-core/livekit_agent.py` correctly uses the Agents worker pattern, which implies Agent Dispatch should be used on the control plane. No other usage pattern of livekit-api in repo.
- Version compatibility
  - Agents 1.2.2 and API 1.0.5 are compatible for the recommended patterns. Incompatibility stems from usage patterns, not versions.

### 5) Implementation Recommendations

#### Immediate Fix (unblock server startup)
1) Replace broken imports and usage in `agent_management.py`
   - Use `LiveKitAPI` to create rooms, not `RoomService` directly.
   - Update token TTL usage to pass `timedelta`.
2) Lazy-initialize LiveKit manager
   - Avoid constructing `LiveKitManager()` at import-time (it throws if env vars missing). Initialize in endpoint or guard with try/except and clear error messages.
3) Temporarily skip launching the agent process (or guard it behind a feature flag) to avoid launching a worker that won’t receive a dispatch. Continue returning user token and room URL so mobile can connect for testing.
4) Add `livekit-api>=1.0.5` to requirements.txt to pin dependency.

#### Proper Implementation (align with Agents pattern)
- Adopt Agent Dispatch fully:
  - Worker: Keep `agents.cli.run_app(...)` entrypoint.
  - API on session start:
    - Create room via `LiveKitAPI().room.create_room(CreateRoomRequest(...))`.
    - Create an Agent Dispatch via `LiveKitAPI().agent_dispatch.create_agent_dispatch(...)` for the target room. This will cause LiveKit to dispatch a `JobContext` to your running worker, which then joins the room.
    - Do NOT pass agent tokens/args to the worker; the worker should authenticate via `LIVEKIT_API_KEY/SECRET` and receive jobs via dispatch.
  - On session end: delete/cleanup the agent dispatch and optionally the room.

- Alternative (token-based manual join)
  - If choosing not to use Agent Dispatch, refactor the worker to join RTC as a participant using the provided token and room. This requires changing `entrypoint(JobContext)` to an RTC join flow and implementing room connection and media pipelines manually or via Agents’ RTC helpers.
  - This is not recommended given the current code aligns with Agent Dispatch.

#### Required environment setup
- Mandatory
  - LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET (for both API service and the worker environment).
  - GROQ_API_KEY (STT), DEEPINFRA_API_KEY (TTS).
- LiveKit Cloud configuration
  - Define an Agent Dispatch (or have API create it per-session) that targets your worker’s `entrypoint`. Ensure the worker is running and reachable from LiveKit Cloud.

---

## File-by-File Notes

- app/api/agent_management.py
  - Problems
    - `from livekit.api import RoomService` not exported; will raise ImportError.
    - Wrong constructor signature even if imported from `livekit.api.room_service` (missing aiohttp session).
    - `AccessToken.with_ttl` called with datetime; must be `timedelta`.
    - Launching `darvis-ai-core/livekit_agent.py` with `--room/--token` has no effect; script ignores CLI args.
    - Global `livekit_manager = LiveKitManager()` can break app startup without env.
  - Suggested corrections (pattern)
    - Use `from livekit import api as lkapi` and then `async with lkapi.LiveKitAPI(url, key, secret) as client: await client.room.create_room(CreateRoomRequest(...))`.
    - `token.with_ttl(timedelta(hours=...))` for both user and agent tokens.
    - Initialize manager lazily in endpoint.
    - For now, guard or disable agent subprocess launch until Agent Dispatch is wired.

- darvis-ai-core/livekit_agent.py
  - Generally aligned with LiveKit Agents worker model.
  - Assumes Agent Dispatch; expects `JobContext` to be delivered by LiveKit.
  - STT/TTS: Non-streaming implementations; acceptable for initial functionality; consider streaming later for latency.

- requirements.txt
  - Missing `livekit-api`; add `livekit-api>=1.0.5`.

- .env / .env.example
  - Correct LIVEKIT vars present; ensure real values set in deployment.

---

## Next Steps Priority List
1) Fix server startup/import issues in `agent_management.py`
   - Switch to `LiveKitAPI` and correct TTL usage.
   - Lazy-init LiveKit manager.
   - Add `livekit-api` to requirements.
2) Decide integration path (recommended: Agent Dispatch)
   - Implement Agent Dispatch creation on session start and cleanup on end.
   - Verify worker receives jobs and joins room.
3) Validate end-to-end voice session
   - Create room, dispatch agent, mobile app joins, voice exchange occurs.
4) Enhancements (after basic functionality)
   - Map session->process/dispatch IDs for termination and monitoring.
   - Introduce health checks and observability (logs/metrics) for agent runs.
   - Consider streaming STT/TTS for better latency.

---

## Success Criteria
- Immediate path
  - Server starts without LiveKit import/runtime errors.
  - `/api/v1/agent/start_voice_session` returns a valid room, user token, and URL.
- Proper path
  - Starting a voice session creates room and dispatch; worker joins room and responds via TTS.
- Resource requirements
  - LiveKit Cloud project with API key/secret, configured URL.
  - GROQ and DeepInfra API keys set.
  - `livekit-api` added to dependencies to ensure consistent environments.
- Timeline impact
  - Immediate fixes: same day.
  - Agent Dispatch wiring: 0.5–1.5 days depending on testing and LiveKit Cloud setup.
  - Streaming optimizations and production hardening: subsequent iterations.

---

## Answers to Specific Questions
- Is LiveKit actually functional anywhere?
  - The worker module is set up correctly for LiveKit Agents, but end-to-end session flow is currently non-functional due to API/dispatch mismatch. Token generation will also fail due to TTL bug.
- What’s the disconnect between imports and installed package?
  - `RoomService` isn’t exported from `livekit.api`; use `LiveKitAPI` instead. Also the `RoomService` constructor is being used incorrectly.
- Are there working examples to follow?
  - Yes: follow the `LiveKitAPI` pattern and the Agents worker pattern already used in `livekit_agent.py`. For server APIs, `LiveKitAPI().room.create_room(...)` etc.
- Required environment/config?
  - LIVEKIT_URL/API_KEY/API_SECRET for both API and worker; Agent Dispatch must be configured or created. GROQ_API_KEY and DEEPINFRA_API_KEY for STT/TTS.
- Fix current implementation or refactor?
  - Refactor API to align with Agent Dispatch (recommended). If opting for manual join, refactor the worker accordingly.
