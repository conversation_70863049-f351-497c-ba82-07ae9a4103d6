"""
VCF (vCard) file generation utility for contact export.

This module provides functionality to generate vCard 3.0 format files
for contact sharing and device integration.
"""

import re
from typing import Optional, Dict, Any
from datetime import datetime
from app.db.models import Contact


def escape_vcf_text(text: str) -> str:
    """
    Escape special characters in vCard text fields according to RFC 2426.
    
    Args:
        text: Text to escape
        
    Returns:
        Escaped text safe for vCard format
    """
    if not text:
        return ""
    
    # Escape special characters
    text = text.replace("\\", "\\\\")  # Backslash must be first
    text = text.replace(",", "\\,")    # Comma
    text = text.replace(";", "\\;")    # Semicolon
    text = text.replace("\n", "\\n")   # Newline
    text = text.replace("\r", "")      # Remove carriage returns
    
    return text


def fold_vcf_line(line: str, max_length: int = 75) -> str:
    """
    Fold long vCard lines according to RFC 2426.
    
    Lines longer than 75 characters should be folded with CRLF followed by space.
    
    Args:
        line: Line to fold
        max_length: Maximum line length before folding
        
    Returns:
        Folded line with proper line breaks
    """
    if len(line) <= max_length:
        return line
    
    folded_lines = []
    while len(line) > max_length:
        # Find a good break point (prefer not to break in the middle of a word)
        break_point = max_length
        if line[max_length] != ' ':
            # Look for a space before the max length
            space_pos = line.rfind(' ', 0, max_length)
            if space_pos > max_length - 20:  # Don't break too early
                break_point = space_pos
        
        folded_lines.append(line[:break_point])
        line = " " + line[break_point:].lstrip()  # Continuation line starts with space
    
    if line.strip():  # Add remaining text if any
        folded_lines.append(line)
    
    return "\r\n".join(folded_lines)


def format_phone_for_vcf(phone: str) -> str:
    """
    Format phone number for vCard.
    
    Args:
        phone: Phone number to format
        
    Returns:
        Formatted phone number
    """
    if not phone:
        return ""
    
    # Remove all non-digit characters except +
    cleaned = re.sub(r'[^\d+]', '', phone)
    
    # Ensure it starts with + for international format
    if not cleaned.startswith('+'):
        # Assume US number if no country code
        if len(cleaned) == 10:
            cleaned = '+1' + cleaned
        elif len(cleaned) == 11 and cleaned.startswith('1'):
            cleaned = '+' + cleaned
    
    return cleaned


def generate_social_media_urls(social_media: Dict[str, str]) -> list:
    """
    Generate social media URLs for vCard.
    
    Args:
        social_media: Dictionary of platform -> username
        
    Returns:
        List of formatted URLs
    """
    urls = []
    
    # Common social media URL patterns
    url_patterns = {
        'twitter': 'https://twitter.com/{username}',
        'instagram': 'https://instagram.com/{username}',
        'linkedin': 'https://linkedin.com/in/{username}',
        'facebook': 'https://facebook.com/{username}',
        'tiktok': 'https://tiktok.com/@{username}',
        'youtube': 'https://youtube.com/@{username}',
        'snapchat': 'https://snapchat.com/add/{username}',
        'discord': 'https://discord.com/users/{username}',
        'telegram': 'https://t.me/{username}',
        'whatsapp': 'https://wa.me/{username}'
    }
    
    for platform, username in social_media.items():
        if username and platform.lower() in url_patterns:
            # Clean username (remove @ if present)
            clean_username = username.lstrip('@')
            url = url_patterns[platform.lower()].format(username=clean_username)
            urls.append(url)
    
    return urls


def generate_vcf_content(contact: Contact) -> str:
    """
    Generate vCard 3.0 content for a contact.
    
    Args:
        contact: Contact object to convert to vCard
        
    Returns:
        vCard 3.0 formatted string
    """
    lines = []
    
    # vCard header
    lines.append("BEGIN:VCARD")
    lines.append("VERSION:3.0")
    
    # Name fields (required)
    escaped_name = escape_vcf_text(contact.name)
    lines.append(f"FN:{escaped_name}")
    
    # Structured name (N field) - try to parse first/last name
    name_parts = contact.name.split()
    if len(name_parts) >= 2:
        last_name = escape_vcf_text(name_parts[-1])
        first_name = escape_vcf_text(" ".join(name_parts[:-1]))
        lines.append(f"N:{last_name};{first_name};;;")
    else:
        lines.append(f"N:{escaped_name};;;;")
    
    # Phone number
    if contact.phone:
        formatted_phone = format_phone_for_vcf(contact.phone)
        lines.append(f"TEL;TYPE=CELL:{formatted_phone}")
    
    # Email
    if contact.email:
        lines.append(f"EMAIL;TYPE=HOME:{contact.email}")
    
    # Organization/Location (use location or met_at)
    if contact.location:
        escaped_location = escape_vcf_text(contact.location)
        lines.append(f"ORG:Met at: {escaped_location}")
    elif contact.met_at:
        escaped_met_at = escape_vcf_text(contact.met_at)
        lines.append(f"ORG:Met: {escaped_met_at}")
    
    # Social media URLs
    if contact.social_media:
        social_urls = generate_social_media_urls(contact.social_media)
        for url in social_urls:
            lines.append(f"URL:{url}")
    
    # Notes (memory prompt)
    if contact.memory_prompt:
        escaped_notes = escape_vcf_text(contact.memory_prompt)
        lines.append(f"NOTE:{escaped_notes}")
    
    # Photo (if available)
    if contact.image_path:
        lines.append(f"PHOTO;VALUE=URI:{contact.image_path}")
    
    # Revision timestamp
    if contact.updated_at:
        timestamp = contact.updated_at.strftime("%Y%m%dT%H%M%SZ")
        lines.append(f"REV:{timestamp}")
    
    # vCard footer
    lines.append("END:VCARD")
    
    # Fold long lines and join with CRLF
    folded_lines = [fold_vcf_line(line) for line in lines]
    return "\r\n".join(folded_lines)


def generate_vcf_filename(contact: Contact) -> str:
    """
    Generate a safe filename for the vCard file.
    
    Args:
        contact: Contact object
        
    Returns:
        Safe filename for the vCard file
    """
    # Clean the name for filename use
    safe_name = re.sub(r'[^\w\s-]', '', contact.name)
    safe_name = re.sub(r'[-\s]+', '_', safe_name)
    safe_name = safe_name.strip('_')
    
    if not safe_name:
        safe_name = "contact"
    
    return f"{safe_name}.vcf"


def generate_bulk_vcf_content(contacts: list) -> str:
    """
    Generate vCard content for multiple contacts.
    
    Args:
        contacts: List of Contact objects
        
    Returns:
        Combined vCard content for all contacts
    """
    vcf_contents = []
    
    for contact in contacts:
        vcf_content = generate_vcf_content(contact)
        vcf_contents.append(vcf_content)
    
    return "\r\n\r\n".join(vcf_contents)
