"""
Performance tests for Smart Capture API endpoints.

Tests to verify sub-200ms response times for critical operations.
"""

import pytest
import time
import asyncio
from fastapi.testclient import TestClient
from app.main import app
from app.db.database import get_db, SessionLocal
from app.db import crud
from app.db.models import InboxItem
import uuid

client = TestClient(app)

# Mock user for testing
MOCK_USER = {
    "uid": "test_user_123",
    "email": "<EMAIL>"
}

class TestSmartCapturePerformance:
    """Performance tests for Smart Capture functionality."""
    
    def setup_method(self):
        """Set up test data."""
        self.db = SessionLocal()
        self.user_id = MOCK_USER["uid"]
        
        # Create test inbox items
        self.test_items = []
        for i in range(10):
            item = crud.create_inbox_item(
                db=self.db,
                user_id=self.user_id,
                original_url=f"https://example.com/article-{i}",
                tags=[f"tag{i}", "test"],
                category="technology"
            )
            self.test_items.append(item)
        
        self.db.commit()
    
    def teardown_method(self):
        """Clean up test data."""
        # Delete test items
        for item in self.test_items:
            self.db.delete(item)
        self.db.commit()
        self.db.close()
    
    def test_search_performance(self):
        """Test search endpoint performance - should be under 200ms."""
        start_time = time.time()
        
        response = client.get(
            "/api/v1/inbox/search",
            params={
                "query": "test",
                "limit": 20,
                "page": 1
            },
            headers={"Authorization": "Bearer mock_token"}
        )
        
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        print(f"Search response time: {response_time_ms:.2f}ms")
        
        # Performance assertion
        assert response_time_ms < 200, f"Search took {response_time_ms:.2f}ms, should be under 200ms"
        assert response.status_code == 200
    
    def test_tags_endpoint_performance(self):
        """Test tags endpoint performance with caching."""
        # First request (cache miss)
        start_time = time.time()
        response1 = client.get(
            "/api/v1/inbox/tags",
            headers={"Authorization": "Bearer mock_token"}
        )
        end_time = time.time()
        first_response_time_ms = (end_time - start_time) * 1000
        
        # Second request (cache hit)
        start_time = time.time()
        response2 = client.get(
            "/api/v1/inbox/tags",
            headers={"Authorization": "Bearer mock_token"}
        )
        end_time = time.time()
        second_response_time_ms = (end_time - start_time) * 1000
        
        print(f"Tags first request: {first_response_time_ms:.2f}ms")
        print(f"Tags cached request: {second_response_time_ms:.2f}ms")
        
        # Performance assertions
        assert first_response_time_ms < 200, f"First request took {first_response_time_ms:.2f}ms"
        assert second_response_time_ms < 50, f"Cached request took {second_response_time_ms:.2f}ms"
        assert response1.status_code == 200
        assert response2.status_code == 200
        assert response1.json() == response2.json()  # Same data
    
    def test_categories_endpoint_performance(self):
        """Test categories endpoint performance with caching."""
        start_time = time.time()
        
        response = client.get(
            "/api/v1/inbox/categories",
            headers={"Authorization": "Bearer mock_token"}
        )
        
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        print(f"Categories response time: {response_time_ms:.2f}ms")
        
        # Performance assertion
        assert response_time_ms < 200, f"Categories took {response_time_ms:.2f}ms"
        assert response.status_code == 200
    
    def test_bulk_operations_performance(self):
        """Test bulk operations performance."""
        # Test bulk categorize
        item_ids = [item.id for item in self.test_items[:5]]
        
        start_time = time.time()
        
        response = client.post(
            "/api/v1/inbox/bulk-categorize",
            json={
                "item_ids": item_ids,
                "category": "business"
            },
            headers={"Authorization": "Bearer mock_token"}
        )
        
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        print(f"Bulk categorize response time: {response_time_ms:.2f}ms")
        
        # Performance assertion
        assert response_time_ms < 500, f"Bulk categorize took {response_time_ms:.2f}ms"
        assert response.status_code == 200
    
    def test_pagination_performance(self):
        """Test pagination performance with large datasets."""
        # Test different page sizes
        page_sizes = [10, 20, 50]
        
        for page_size in page_sizes:
            start_time = time.time()
            
            response = client.get(
                "/api/v1/inbox/search",
                params={
                    "limit": page_size,
                    "page": 1
                },
                headers={"Authorization": "Bearer mock_token"}
            )
            
            end_time = time.time()
            response_time_ms = (end_time - start_time) * 1000
            
            print(f"Pagination (limit={page_size}) response time: {response_time_ms:.2f}ms")
            
            # Performance assertion
            assert response_time_ms < 200, f"Pagination with limit {page_size} took {response_time_ms:.2f}ms"
            assert response.status_code == 200

class TestDatabaseQueryPerformance:
    """Test database query performance directly."""
    
    def setup_method(self):
        """Set up test database."""
        self.db = SessionLocal()
        self.user_id = "perf_test_user"
        
        # Create test data
        self.test_items = []
        for i in range(100):  # Larger dataset for performance testing
            item = crud.create_inbox_item(
                db=self.db,
                user_id=self.user_id,
                original_url=f"https://example.com/perf-test-{i}",
                tags=[f"tag{i % 10}", "performance", "test"],
                category=["technology", "business", "science"][i % 3]
            )
            self.test_items.append(item)
        
        self.db.commit()
    
    def teardown_method(self):
        """Clean up test data."""
        for item in self.test_items:
            self.db.delete(item)
        self.db.commit()
        self.db.close()
    
    def test_search_query_performance(self):
        """Test direct database search query performance."""
        start_time = time.time()
        
        results = crud.search_inbox_items(
            db=self.db,
            user_id=self.user_id,
            query="test",
            tags=["performance"],
            limit=20,
            skip=0
        )
        
        end_time = time.time()
        query_time_ms = (end_time - start_time) * 1000
        
        print(f"Database search query time: {query_time_ms:.2f}ms")
        print(f"Results found: {len(results)}")
        
        # Performance assertion
        assert query_time_ms < 100, f"Database query took {query_time_ms:.2f}ms, should be under 100ms"
        assert len(results) > 0
    
    def test_bulk_operations_query_performance(self):
        """Test bulk operations database performance."""
        item_ids = [item.id for item in self.test_items[:50]]
        
        start_time = time.time()
        
        updated_count = crud.bulk_update_category(
            db=self.db,
            user_id=self.user_id,
            item_ids=item_ids,
            category="performance_test"
        )
        
        end_time = time.time()
        query_time_ms = (end_time - start_time) * 1000
        
        print(f"Bulk update query time: {query_time_ms:.2f}ms")
        print(f"Items updated: {updated_count}")
        
        # Performance assertion
        assert query_time_ms < 200, f"Bulk update took {query_time_ms:.2f}ms"
        assert updated_count == len(item_ids)

class TestContentProcessingPipeline:
    """Test content processing pipeline functionality."""

    def setup_method(self):
        """Set up test environment."""
        self.db = SessionLocal()
        self.user_id = "pipeline_test_user"

    def teardown_method(self):
        """Clean up test environment."""
        # Clean up test items
        items = self.db.query(InboxItem).filter(InboxItem.owner_id == self.user_id).all()
        for item in items:
            self.db.delete(item)
        self.db.commit()
        self.db.close()

    def test_metadata_extraction_performance(self):
        """Test metadata extraction performance."""
        from app.worker import extract_content_metadata

        sample_content = """
        # AI Revolution in Healthcare

        By Dr. Jane Smith
        Published on March 15, 2024

        Artificial intelligence is transforming healthcare in unprecedented ways.
        This comprehensive analysis explores the latest developments in AI-powered
        medical diagnostics, treatment planning, and patient care optimization.

        The integration of machine learning algorithms with medical imaging has
        resulted in diagnostic accuracy improvements of up to 95% in certain
        cancer detection scenarios.
        """ * 10  # Make it longer for realistic testing

        start_time = time.time()

        metadata = extract_content_metadata(sample_content, "https://example.com/ai-healthcare")

        end_time = time.time()
        processing_time_ms = (end_time - start_time) * 1000

        print(f"Metadata extraction time: {processing_time_ms:.2f}ms")

        # Performance assertion
        assert processing_time_ms < 1000, f"Metadata extraction took {processing_time_ms:.2f}ms"

        # Verify metadata quality
        assert metadata["title"] is not None
        assert metadata["word_count"] > 0
        assert metadata["reading_time"] > 0
        assert metadata["content_type"] == "article"
        assert metadata["category"] is not None

    def test_content_categorization_accuracy(self):
        """Test content categorization accuracy."""
        from app.worker import generate_content_category

        test_cases = [
            ("Python programming tutorial for beginners", "technology"),
            ("Stock market analysis and investment strategies", "finance"),
            ("Healthy recipes for weight loss", "health"),
            ("Latest football match results and analysis", "sports"),
            ("Climate change impact on global economy", "science")
        ]

        correct_predictions = 0
        total_time = 0

        for content, expected_category in test_cases:
            start_time = time.time()
            predicted_category = generate_content_category(content)
            end_time = time.time()

            processing_time = (end_time - start_time) * 1000
            total_time += processing_time

            print(f"Content: '{content[:50]}...' -> Predicted: {predicted_category}, Expected: {expected_category}")

            # Check if prediction is reasonable (exact match or related)
            if predicted_category == expected_category or predicted_category in ["technology", "business", "other"]:
                correct_predictions += 1

        avg_time = total_time / len(test_cases)
        accuracy = correct_predictions / len(test_cases)

        print(f"Average categorization time: {avg_time:.2f}ms")
        print(f"Categorization accuracy: {accuracy:.2%}")

        # Performance and accuracy assertions
        assert avg_time < 2000, f"Average categorization time {avg_time:.2f}ms too slow"
        assert accuracy >= 0.6, f"Categorization accuracy {accuracy:.2%} too low"

if __name__ == "__main__":
    # Run performance tests
    pytest.main([__file__, "-v", "-s"])
