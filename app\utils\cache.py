"""
Caching utilities for Smart Capture performance optimization.

This module provides Redis-based caching for frequently accessed data
to achieve sub-200ms response times for API operations.
"""

import json
import logging
from typing import Any, Optional, Dict, List
from functools import wraps
from app.db.database import get_redis
import hashlib

logger = logging.getLogger(__name__)

class SmartCaptureCache:
    """Cache manager for Smart Capture data."""
    
    def __init__(self):
        self.redis_client = get_redis()
        self.default_ttl = 3600  # 1 hour default TTL
        
    def _get_cache_key(self, prefix: str, user_id: str, **kwargs) -> str:
        """Generate cache key from parameters."""
        # Create a deterministic key from parameters
        key_parts = [prefix, user_id]
        
        # Sort kwargs for consistent key generation
        for key, value in sorted(kwargs.items()):
            if value is not None:
                key_parts.append(f"{key}:{value}")
        
        cache_key = ":".join(key_parts)
        
        # Hash long keys to avoid Redis key length limits
        if len(cache_key) > 200:
            cache_key = f"{prefix}:{user_id}:{hashlib.md5(cache_key.encode()).hexdigest()}"
        
        return cache_key
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if not self.redis_client:
            return None
            
        try:
            cached_value = self.redis_client.get(key)
            if cached_value:
                return json.loads(cached_value)
        except Exception as e:
            logger.warning(f"Cache get error for key {key}: {e}")
        
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        if not self.redis_client:
            return False
            
        try:
            ttl = ttl or self.default_ttl
            serialized_value = json.dumps(value, default=str)  # default=str handles datetime objects
            self.redis_client.setex(key, ttl, serialized_value)
            return True
        except Exception as e:
            logger.warning(f"Cache set error for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        if not self.redis_client:
            return False
            
        try:
            self.redis_client.delete(key)
            return True
        except Exception as e:
            logger.warning(f"Cache delete error for key {key}: {e}")
            return False
    
    def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern."""
        if not self.redis_client:
            return 0
            
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return 0
        except Exception as e:
            logger.warning(f"Cache delete pattern error for pattern {pattern}: {e}")
            return 0
    
    def cache_search_results(self, user_id: str, search_params: Dict[str, Any], 
                           results: List[Dict], total: int, ttl: int = 300) -> str:
        """Cache search results with 5-minute TTL."""
        cache_key = self._get_cache_key("search", user_id, **search_params)
        
        cache_data = {
            "results": results,
            "total": total,
            "cached_at": str(json.dumps(None))  # Will be handled by json.dumps default
        }
        
        self.set(cache_key, cache_data, ttl)
        return cache_key
    
    def get_cached_search_results(self, user_id: str, search_params: Dict[str, Any]) -> Optional[Dict]:
        """Get cached search results."""
        cache_key = self._get_cache_key("search", user_id, **search_params)
        return self.get(cache_key)
    
    def cache_user_tags(self, user_id: str, tags: List[str], ttl: int = 1800) -> None:
        """Cache user's available tags with 30-minute TTL."""
        cache_key = f"tags:{user_id}"
        self.set(cache_key, tags, ttl)
    
    def get_cached_user_tags(self, user_id: str) -> Optional[List[str]]:
        """Get cached user tags."""
        cache_key = f"tags:{user_id}"
        return self.get(cache_key)
    
    def cache_user_categories(self, user_id: str, categories: List[str], ttl: int = 1800) -> None:
        """Cache user's available categories with 30-minute TTL."""
        cache_key = f"categories:{user_id}"
        self.set(cache_key, categories, ttl)
    
    def get_cached_user_categories(self, user_id: str) -> Optional[List[str]]:
        """Get cached user categories."""
        cache_key = f"categories:{user_id}"
        return self.get(cache_key)
    
    def invalidate_user_cache(self, user_id: str) -> None:
        """Invalidate all cache entries for a user."""
        patterns = [
            f"search:{user_id}:*",
            f"tags:{user_id}",
            f"categories:{user_id}",
            f"inbox_item:{user_id}:*"
        ]
        
        for pattern in patterns:
            deleted_count = self.delete_pattern(pattern)
            if deleted_count > 0:
                logger.info(f"Invalidated {deleted_count} cache entries for pattern: {pattern}")

# Global cache instance
smart_capture_cache = SmartCaptureCache()

def cache_response(ttl: int = 300, key_prefix: str = "api"):
    """
    Decorator to cache API response data.
    
    Args:
        ttl: Time to live in seconds (default: 5 minutes)
        key_prefix: Prefix for cache key
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user_id from kwargs (assuming it's passed as current_user)
            current_user = kwargs.get('current_user')
            if not current_user:
                # If no user context, don't cache
                return func(*args, **kwargs)
            
            user_id = current_user.get('uid')
            if not user_id:
                return func(*args, **kwargs)
            
            # Generate cache key from function name and parameters
            cache_key = smart_capture_cache._get_cache_key(
                f"{key_prefix}:{func.__name__}",
                user_id,
                **{k: v for k, v in kwargs.items() if k != 'current_user' and k != 'db'}
            )
            
            # Try to get from cache
            cached_result = smart_capture_cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            
            # Cache the result (convert to dict if it's a Pydantic model)
            if hasattr(result, 'dict'):
                cache_data = result.dict()
            elif hasattr(result, '__dict__'):
                cache_data = result.__dict__
            else:
                cache_data = result
            
            smart_capture_cache.set(cache_key, cache_data, ttl)
            logger.debug(f"Cached result for key: {cache_key}")
            
            return result
        
        return wrapper
    return decorator

def invalidate_user_cache_on_update(func):
    """Decorator to invalidate user cache when data is updated."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        
        # Extract user_id from kwargs
        current_user = kwargs.get('current_user')
        if current_user:
            user_id = current_user.get('uid')
            if user_id:
                smart_capture_cache.invalidate_user_cache(user_id)
                logger.debug(f"Invalidated cache for user: {user_id}")
        
        return result
    
    return wrapper
